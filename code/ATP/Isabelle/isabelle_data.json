[{"problem": "theory LogicEx1\n  imports Main\nbegin\n\ndefinition convex :: \"(int \\<Rightarrow> int) \\<Rightarrow> bool\" where\n  \"convex f \\<longleftrightarrow> (\\<forall> x. f (x - 1) + f (x + 1) \\<ge> 2 * f x)\"\n\ndefinition mono :: \"(int \\<Rightarrow> int) \\<Rightarrow> bool\" where\n  \"mono f \\<longleftrightarrow> (\\<forall> n m. n \\<le> m \\<longrightarrow> f n \\<le> f m)\"\n\ntheorem logic_ex1:\n  assumes \"\\<forall> f. mono f \\<longrightarrow> mono (T f)\"\n    and \"\\<forall> f. convex f \\<longrightarrow> convex (T f)\"\n    and \"mono f \\<and> convex f\"\n  shows \"mono (T f) \\<and> convex (T f)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms(1) have \"mono f \\<longrightarrow> mono (T f)\" by auto\n  from assms(2) have \"convex f \\<longrightarrow> convex (T f)\" by auto\n  from assms(3) have \"mono f\" and \"convex f\" by auto\n  thus \"mono (T f) \\<and> convex (T f)\" using assms by auto\nqed\n\nend", "theorem": "logic_ex1"}, {"problem": "theory PlusAssoc\n  imports Main\nbegin\n\n\n(* 定义关联性（associativity） *)\ndefinition assoc :: \"(int \\<Rightarrow> int \\<Rightarrow> int) \\<Rightarrow> bool\" where\n  \"assoc f \\<longleftrightarrow> (\\<forall>x y z. f x (f y z) = f (f x y) z)\"\n\n(* 证明加法是关联的 *)\nlemma plus_assoc: \"assoc (\\<lambda>x y. x + y)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  (* 展开定义 *)\n  have \"\\<forall>x y z. (x + (y + z)) = ((x + y) + z)\" \n    by auto\n  thus ?thesis \n    unfolding assoc_def\n    by auto\nqed\n\nend", "theorem": "plus_assoc"}, {"problem": "theory OrIntrol\n  imports Main\nbegin\n\n\nlemma or_introl:\n  fixes A B :: \"bool\"\n  assumes H: \"A\"\n  shows \"A \\<or> B\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  show \"A \\<or> B\" using H by (rule disjI1)\nqed\n\nend", "theorem": "or_introl"}, {"problem": "theory OrAndDistrL\n  imports Main\nbegin\n\nlemma or_and_distr_l:\n  \"P \\<or> (Q \\<and> R) \\<longleftrightarrow> (P \\<or> Q) \\<and> (P \\<or> R)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  assume \"P \\<or> (Q \\<and> R)\"\n  then show \"(P \\<or> Q) \\<and> (P \\<or> R)\"\n  proof\n    assume P: \"P\"\n    then have \"P \\<or> Q\" and \"P \\<or> R\" by simp_all\n    thus ?thesis by simp\n  next\n    assume \"Q \\<and> R\"\n    then have Q: \"Q\" and R: \"R\" by simp_all\n    then have \"P \\<or> Q\" and \"P \\<or> R\" by simp_all\n    thus ?thesis by simp\n  qed\nnext\n  assume \"(P \\<or> Q) \\<and> (P \\<or> R)\"\n  then have PQ: \"P \\<or> Q\" and PR: \"P \\<or> R\" by simp_all\n  show \"P \\<or> (Q \\<and> R)\"\n  proof cases\n    assume \"P\"\n    thus ?thesis by simp\n  next\n    assume \"\\<not> P\"\n    with PQ have Q: \"Q\" by blast\n    with PR have R: \"R\" by blast\n    from Q R have \"Q \\<and> R\" by simp\n    thus ?thesis by simp\n  qed\nqed\n\nend", "theorem": "or_and_distr_l"}, {"problem": "theory Proj2\n  imports Main\nbegin\n\n\n\nlemma proj2:\n  fixes P Q :: \"bool\"\n  assumes H: \"P \\<and> Q\"\n  shows \"Q\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from H show \"Q\" by (rule conjunct2)\nqed\n\nend", "theorem": "proj2"}, {"problem": "theory ShiftUp1Square\n  imports Main\nbegin\n\ndefinition square :: \"int \\<Rightarrow> int\" where\n\"square x = x * x\"\n\ndefinition shift_up1 :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n\"shift_up1 f x = f x + 1\"\n\nlemma shift_up1_square: \"shift_up1 square x = x * x + 1\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding shift_up1_def square_def\n  by simp\n\nend", "theorem": "shift_up1_square"}, {"problem": "theory LeafHeight\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\nfun tree_height :: \"tree \\<Rightarrow> int\" where\n\"tree_height Leaf = 0\" |\n\"tree_height (Node l v r) = max (tree_height l) (tree_height r) + 1\"\n\nlemma Leaf_height: \"tree_height Leaf = 0\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  show ?thesis by simp\nqed\n\nend", "theorem": "Leaf_height"}, {"problem": "theory LogicEx2\n  imports Main\nbegin\n\nlemma logic_ex2:\n  assumes \"P1 \\<and> Q1\"\n  and \"P1 \\<Longrightarrow> P2\"\n  and \"Q1 \\<Longrightarrow> Q2\"\n  shows \"P2 \\<and> Q2\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms(1) have P1 by simp\n  from assms(1) have Q1 by simp\n  from assms(2) and `P1` have P2 by simp\n  from assms(3) and `Q1` have Q2 by simp\n  thus \"P2 \\<and> Q2\" by simp\nqed\n\nend", "theorem": "logic_ex2"}, {"problem": "theory ForallIff\n  imports Main\nbegin\n\ntheorem forall_iff:\n  fixes P Q :: \"'a \\<Rightarrow> bool\"\n  assumes \"\\<forall>x. P x \\<longleftrightarrow> Q x\"\n  shows \"(\\<forall>x. P x) \\<longleftrightarrow> (\\<forall>x. Q x)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  -- \"First, show (\\<forall>x. P x) \\<Longrightarrow> (\\<forall>x. Q x)\"\n  assume \"\\<forall>x. P x\"\n  show \"\\<forall>x. Q x\"\n  proof\n    fix x\n    from assms have \"P x \\<longleftrightarrow> Q x\" by auto\n    with \\<open>\\<forall>x. P x\\<close> show \"Q x\" by auto\n  qed\nnext\n  -- \"Now, show (\\<forall>x. Q x) \\<Longrightarrow> (\\<forall>x. P x)\"\n  assume \"\\<forall>x. Q x\"\n  show \"\\<forall>x. P x\"\n  proof\n    fix x\n    from assms have \"P x \\<longleftrightarrow> Q x\" by auto\n    with \\<open>\\<forall>x. Q x\\<close> show \"P x\" by auto\n  qed\nqed\n\nend", "theorem": "forall_iff"}, {"problem": "theory DoubleNegationIff\n  imports Main\nbegin\n\ntheorem double_negation_iff: \"\\<not>\\<not>P \\<longleftrightarrow> P\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  show \"\\<not>\\<not>P \\<longrightarrow> P\"\n  proof (rule classical)\n    assume \"\\<not>\\<not>P\"\n    show \"P\"\n    proof (rule classical)\n      assume \"\\<not>P\"\n      thus False using \\<open>\\<not>\\<not>P\\<close> by contradiction\n    qed\n  qed\nnext\n  show \"P \\<longrightarrow> \\<not>\\<not>P\"\n  proof\n    assume \"P\"\n    show \"\\<not>\\<not>P\"\n    proof\n      assume \"\\<not>P\"\n      thus False using \\<open>P\\<close> by contradiction\n    qed\n  qed\nqed\n\nend", "theorem": "double_negation_iff"}, {"problem": "theory MonoPu\n  imports Main \nbegin\n\n(* 定义单调性 *)\ndefinition mono :: \"(int \\<Rightarrow> int) \\<Rightarrow> bool\" where\n  \"mono f \\<equiv> \\<forall>n m. n \\<le> m \\<longrightarrow> f n \\<le> f m\"\n\n(* 定义 shift_up1 函数 *)\ndefinition shift_up1 :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n  \"shift_up1 f x \\<equiv> f x + 1\"\n\n(* 定义命题在 shift_up1 下保持 *)\ndefinition preserved_by_shifting_up :: \"((int \\<Rightarrow> int) \\<Rightarrow> bool) \\<Rightarrow> bool\" where\n  \"preserved_by_shifting_up P \\<equiv> \\<forall>f. P f \\<longrightarrow> P (shift_up1 f)\"\n\n(* 证明单调性在 shift_up1 下保持 *)\nlemma mono_pu: \"preserved_by_shifting_up mono\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding preserved_by_shifting_up_def mono_def shift_up1_def\n  by auto\n\nend", "theorem": "mono_pu"}, {"problem": "theory Treeexample3bSize\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\ndefinition tree_example2b :: tree where\n\"tree_example2b = Node (Node Leaf 9 Leaf) 100 (Node Leaf 8 Leaf)\"\n\ndefinition tree_example3b :: tree where\n\"tree_example3b = Node tree_example2b 5 (Node Leaf 3 Leaf)\"\n\nfun tree_size :: \"tree \\<Rightarrow> int\" where\n\"tree_size Leaf = 0\" |\n\"tree_size (Node l v r) = tree_size l + tree_size r + 1\"\n\nlemma treeexample3b_size: \"tree_size tree_example3b = 5\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  show ?thesis\n    unfolding tree_example3b_def tree_example2b_def\n    by simp\nqed\n\nend", "theorem": "treeexample3b_size"}, {"problem": "theory LogicEx3\n  imports Main\nbegin\n\nlemma logic_ex3:\n  fixes P Q :: \"'a \\<Rightarrow> bool\"\n  assumes H: \"\\<forall>a. P a \\<longrightarrow> Q a\"\n  shows \"\\<forall>a. \\<not> Q a \\<longrightarrow> \\<not> P a\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (intro allI impI)\n  fix a\n  assume \"\\<not> Q a\"\n  have \"P a \\<longrightarrow> Q a\" using H by (rule allE)\n  show \"\\<not> P a\"\n  proof\n    assume \"P a\"\n    from `P a \\<longrightarrow> Q a` and `P a` have \"Q a\" by (rule mp)\n    from `\\<not> Q a` and `Q a` show False by contradiction\n  qed\nqed\n\nend", "theorem": "logic_ex3"}, {"problem": "theory Proj1\n  imports Main\nbegin\n\nlemma proj1:\n  fixes P Q :: \"bool\"\n  assumes H: \"P \\<and> Q\"\n  shows \"P\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from H show \"P\" by (rule conjunct1)\nqed\n\nend", "theorem": "proj1"}, {"problem": "theory AndDup\n  imports Main\nbegin\n\nlemma and_dup: \"P \\<and> P \\<longleftrightarrow> P\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  show \"P \\<and> P \\<Longrightarrow> P\" by (erule conjE)\nnext\n  show \"P \\<Longrightarrow> P \\<and> P\" by (rule conjI)\nqed\n\n\nend", "theorem": "and_dup"}, {"problem": "theory FixpointSelfComp23\n  imports Main\nbegin\n\ndefinition Zcomp :: \"(int \\<Rightarrow> int) \\<Rightarrow> (int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n  \"Zcomp f g \\<equiv> \\<lambda> x. f (g x)\"\n\ndefinition is_fixpoint :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> bool\" where\n  \"is_fixpoint f x \\<equiv> f x = x\"\n\nlemma fixpoint_self_comp23:\n  assumes \"is_fixpoint (Zcomp f f) x\"\n      and \"is_fixpoint (Zcomp f (Zcomp f f)) x\"\n    shows \"is_fixpoint f x\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  have H1: \"f (f x) = x\"\n    using assms(1) unfolding is_fixpoint_def Zcomp_def by simp\n  have H2: \"f (f (f x)) = x\"\n    using assms(2) unfolding is_fixpoint_def Zcomp_def by simp\n  have \"f x = f (f (f x))\"\n    using H1 by simp\n  also have \"... = x\"\n    using H2 by simp\n  finally have \"f x = x\" .\n  thus ?thesis\n    unfolding is_fixpoint_def by simp\nqed\n\nend", "theorem": "fixpoint_self_comp23"}, {"problem": "theory LogicEx7\n  imports Main\nbegin\n\n(* 对应 Coq 中的 logic_ex7 事实 *)\ntheorem logic_ex7:\n  assumes \"\\<forall>a. P a \\<longrightarrow> Q a \\<longrightarrow> False\"\n  assumes \"Q a0\"\n  shows \"\\<not> P a0\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  assume \"P a0\"\n  from assms(1) have \"P a0 \\<longrightarrow> Q a0 \\<longrightarrow> False\" by simp\n  hence \"Q a0 \\<longrightarrow> False\" using `P a0` by blast\n  thus False using assms(2) by blast\nqed\n\nend", "theorem": "logic_ex7"}, {"problem": "theory MulAddDistrR\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\nfun mymul :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"mymul MyZero m = MyZero\" |\n  \"mymul (MySuc n) m = myadd m (mymul n m)\"\n\ntheorem myadd_assoc: \"myadd n (myadd m p) = myadd (myadd n m) p\"\n  by (induction n; simp)\n\ntheorem mul_add_distr_r: \"mymul (myadd n m) p = myadd (mymul n p) (mymul m p)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction n)\n  case <PERSON><PERSON><PERSON>\n  then show ?case by simp\nnext\n  case (MySuc n)\n  then show ?case\n    using myadd_assoc by (simp add: myadd_assoc)\nqed\n\nend", "theorem": "mul_add_distr_r"}, {"problem": "theory FixpointSelfComp\n  imports Main\nbegin\n\ndefinition Zcomp :: \"(int \\<Rightarrow> int) \\<Rightarrow> (int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n\"Zcomp f g x = f (g x)\"\n\ndefinition is_fixpoint :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> bool\" where\n\"is_fixpoint f x = (f x = x)\"\n\ntheorem fixpoint_self_comp: \n  assumes \"is_fixpoint f x\"\n  shows \"is_fixpoint (Zcomp f f) x\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  (* 显式展开 is_fixpoint 的定义 *)\n  have \"f x = x\" using assms unfolding is_fixpoint_def by simp\n  (* 接下来展开 Zcomp 的定义 *)\n  have \"Zcomp f f x = f (f x)\" unfolding Zcomp_def by simp\n  (* 利用 f x = x 的假设 *)\n  also have \"... = f x\" using \\<open>f x = x\\<close> by simp\n  also have \"... = x\" using \\<open>f x = x\\<close> by simp\n  finally show ?thesis unfolding is_fixpoint_def by simp\nqed\n\n\nend", "theorem": "fixpoint_self_comp"}, {"problem": "theory AddCancelR\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\ntheorem myadd_comm: \"myadd n m = myadd m n\"\n  sorry\n\nlemma myadd_cancel_l: \"myadd p n = myadd p m \\<longleftrightarrow> n = m\"\n  sorry\n\ntheorem add_cancel_r: \"myadd n p = myadd m p \\<longleftrightarrow> n = m\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  assume \"myadd n p = myadd m p\"\n  then have \"myadd p n = myadd p m\" using myadd_comm by simp\n  then show \"n = m\" using myadd_cancel_l by simp\nnext\n  assume \"n = m\"\n  then show \"myadd n p = myadd m p\" by simp\nqed\n\nend", "theorem": "add_cancel_r"}, {"problem": "theory ShiftLeft1PlusOne\n  imports Main\nbegin\n\ndefinition plus_one :: \"int \\<Rightarrow> int\" where\n\"plus_one x = x + 1\"\n\ndefinition shift_left1 :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n\"shift_left1 f x = f (x + 1)\"\n\nlemma shift_left1_plus_one: \"shift_left1 plus_one x = x + 2\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding shift_left1_def plus_one_def\n  by simp\n\nend", "theorem": "shift_left1_plus_one"}, {"problem": "theory ImplyCongr\n  imports Main\nbegin\n\nlemma imply_congr:\n  \"((P1 \\<longleftrightarrow> P2) \\<and> (Q1 \\<longleftrightarrow> Q2)) \\<longrightarrow> ((P1 \\<longrightarrow> Q1) \\<longleftrightarrow> (P2 \\<longrightarrow> Q2))\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  assume hyps: \"(P1 \\<longleftrightarrow> P2) \\<and> (Q1 \\<longleftrightarrow> Q2)\"\n  then have P_eq: \"P1 \\<longleftrightarrow> P2\" and Q_eq: \"Q1 \\<longleftrightarrow> Q2\" by simp_all\n  show \"(P1 \\<longrightarrow> Q1) \\<longleftrightarrow> (P2 \\<longrightarrow> Q2)\"\n  proof\n    {\n      assume H: \"P1 \\<longrightarrow> Q1\"\n      show \"P2 \\<longrightarrow> Q2\"\n      proof\n        assume P2: \"P2\"\n        with P_eq have P1: \"P1\" using iffD1 by blast\n        from H P1 have Q1: \"Q1\" by (rule mp)\n        from Q_eq Q1 have \"Q2\" using iffD1 by blast\n        thus \"Q2\" by assumption\n      qed\n    }\n    {\n      assume H: \"P2 \\<longrightarrow> Q2\"\n      show \"P1 \\<longrightarrow> Q1\"\n      proof\n        assume P1: \"P1\"\n        with P_eq have P2: \"P2\" using iffD2 by blast\n        from H P2 have Q2: \"Q2\" by (rule mp)\n        from Q_eq Q2 have \"Q1\" using iffD2 by blast\n        thus \"Q1\" by assumption\n      qed\n    }\n  qed\nqed\n\nend", "theorem": "imply_congr"}, {"problem": "theory LogicEx6\n  imports Main\nbegin\n\n(* 对应 Coq 中的 logic_ex6 事实 *)\ntheorem logic_ex6:\n  assumes \"P a0\"\n  assumes \"\\<forall>a. P a \\<longrightarrow> Q a\"\n  shows \"Q a0\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms(2) have \"P a0 \\<longrightarrow> Q a0\" by simp\n  from assms(1) have \"P a0\" by simp\n  thus \"Q a0\" using `P a0 \\<longrightarrow> Q a0` by blast\nqed\n\nend", "theorem": "logic_ex6"}, {"problem": "theory AndIntro\n  imports Main\nbegin\n\nlemma and_intro:\n  fixes A B :: \"bool\"\n  assumes HA: \"A\" and HB: \"B\"\n  shows \"A \\<and> B\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from HA and HB show \"A \\<and> B\"\n    by (rule conjI)\nqed\n\nend", "theorem": "and_intro"}, {"problem": "theory MulAssoc\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\nfun mymul :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"mymul MyZero m = MyZero\" |\n  \"mymul (MySuc n) m = myadd m (mymul n m)\"\n\ntheorem mymul_add_distr_r: \"mymul (myadd n m) p = myadd (mymul n p) (mymul m p)\"\n  sorry\n\ntheorem myadd_assoc: \"myadd n (myadd m p) = myadd (myadd n m) p\"\n  sorry\n\n\ntheorem mul_assoc: \"mymul n (mymul m p) = mymul (mymul n m) p\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction n)\n  case <PERSON><PERSON><PERSON>\n  then show ?case by simp\nnext\n  case (MySuc n)\n  then show ?case\n    using mymul_add_distr_r by (simp add: myadd_assoc)\nqed\n\nend", "theorem": "mul_assoc"}, {"problem": "theory LogicEx4\n  imports Main\nbegin\n\nlemma logic_ex4:\n  fixes P Q :: \"'a \\<Rightarrow> bool\"\n  assumes H: \"\\<forall>a. \\<not> Q a \\<longrightarrow> \\<not> P a\"\n  shows \"\\<forall>a. P a \\<longrightarrow> Q a\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (intro allI impI)\n  fix a\n  assume \"P a\"\n  have \"\\<not> Q a \\<longrightarrow> \\<not> P a\" using H by (rule allE)\n  show \"Q a\"\n  proof (rule ccontr)\n    assume \"\\<not> Q a\"\n    from `\\<not> Q a \\<longrightarrow> \\<not> P a` and `\\<not> Q a` have \"\\<not> P a\" by (rule mp)\n    from `P a` and `\\<not> P a` show False by contradiction\n  qed\nqed\n\nend", "theorem": "logic_ex4"}, {"problem": "theory OrExample\n  imports Main\nbegin\n\nlemma or_example:\n  fixes P Q R :: \"bool\"\n  assumes H1: \"P \\<longrightarrow> R\" and H2: \"Q \\<longrightarrow> R\" and H3: \"P \\<or> Q\"\n  shows \"R\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from H3 show \"R\"\n  proof\n    assume \"P\"\n    from H1 and `P` show \"R\" by (rule mp)\n  next\n    assume \"Q\"\n    from H2 and `Q` show \"R\" by (rule mp)\n  qed\nqed\n\nend", "theorem": "or_example"}, {"problem": "theory TreeExample3TreeReverse\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\ndefinition tree_example2a :: tree where\n\"tree_example2a = Node (Node Leaf 8 Leaf) 100 (Node Leaf 9 Leaf)\"\n\ndefinition tree_example2b :: tree where\n\"tree_example2b = Node (Node Leaf 9 Leaf) 100 (Node Leaf 8 Leaf)\"\n\ndefinition tree_example3a :: tree where\n\"tree_example3a = Node (Node Leaf 3 Leaf) 5 tree_example2a\"\n\ndefinition tree_example3b :: tree where\n\"tree_example3b = Node tree_example2b 5 (Node Leaf 3 Leaf)\"\n\nfun tree_reverse :: \"tree \\<Rightarrow> tree\" where\n\"tree_reverse Leaf = Leaf\" |\n\"tree_reverse (Node l v r) = Node (tree_reverse r) v (tree_reverse l)\"\n\nlemma tree_example3_tree_reverse: \"tree_reverse tree_example3a = tree_example3b\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  show ?thesis\n    unfolding tree_example3a_def tree_example3b_def tree_example2a_def tree_example2b_def\n    by simp\nqed\n\nend", "theorem": "tree_example3_tree_reverse"}, {"problem": "theory MonoCompose\n  imports Main\nbegin\n\ndefinition mono :: \"(int \\<Rightarrow> int) \\<Rightarrow> bool\" where\n\"mono f = (\\<forall>n m. n \\<le> m \\<longrightarrow> f n \\<le> f m)\"\n\ndefinition Zcomp :: \"(int \\<Rightarrow> int) \\<Rightarrow> (int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n\"Zcomp f g x = f (g x)\"\n\nlemma mono_compose: \n  assumes \"mono f\" and \"mono g\"\n  shows \"mono (Zcomp f g)\"\n  unfolding mono_def Zcomp_def\n(* Fill Your Proof Here *)\nend", "solution": "proof (clarify)\n  fix n m :: int\n  assume \"n \\<le> m\"\n  hence \"g n \\<le> g m\" using assms(2) unfolding mono_def by blast\n  thus \"f (g n) \\<le> f (g m)\" using assms(1) unfolding mono_def by blast\nqed\n\nend", "theorem": "mono_compose"}, {"problem": "theory LogicEx5\n  imports Main\nbegin\n\n(* 对应 Coq 中的 logic_ex5 事实 *)\ntheorem logic_ex5:\n  assumes \"\\<forall>a. P a \\<longrightarrow> Q a\"\n  assumes \"\\<forall>a. P a\"\n  shows \"\\<forall>a. Q a\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  fix a\n  from assms(1) have \"P a \\<longrightarrow> Q a\" by simp\n  from assms(2) have \"P a\" by simp\n  thus \"Q a\" using `P a \\<longrightarrow> Q a` by blast\nqed\n\nend", "theorem": "logic_ex5"}, {"problem": "theory Square5\n  imports Main\nbegin\n\ndefinition square :: \"int \\<Rightarrow> int\" where\n\"square x = x * x\"\n\nlemma square_5: \"square 5 = 25\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding square_def\n  by simp\n\nend", "theorem": "square_5"}, {"problem": "theory ForallAnd\n  imports Main\nbegin\n\ntheorem forall_and: \"(\\<forall>a. P a \\<and> Q a) = ((\\<forall>a. P a) \\<and> (\\<forall>a. Q a))\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  {\n    assume \"\\<forall>a. P a \\<and> Q a\"\n    then have \"\\<forall>a. P a\" and \"\\<forall>a. Q a\" by auto\n  }\n  moreover {\n    assume \"(\\<forall>a. P a)\" and \"(\\<forall>a. Q a)\"\n    then have \"\\<forall>a. P a \\<and> Q a\" by auto\n  }\n  ultimately show ?thesis by auto\nqed\n\nend", "theorem": "forall_and"}, {"problem": "theory Add0R\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\nlemma add_0_r: \"myadd n MyZero = n\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction n)\n  case <PERSON><PERSON><PERSON>\n  then show ?case by simp\nnext\n  case (MySuc n)\n  then have \"myadd n MyZero = n\" by simp\n  then show ?case by simp\nqed\n\nend", "theorem": "add_0_r"}, {"problem": "theory OrAndAbsorb\n  imports Main\nbegin\n\nlemma or_and_absorb:\n  \"P \\<or> (P \\<and> Q) \\<longleftrightarrow> P\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  assume \"P \\<or> (P \\<and> Q)\"\n  then show \"P\"\n  proof\n    assume \"P\"\n    thus ?thesis by assumption\n  next\n    assume \"P \\<and> Q\"\n    then show \"P\" by simp\n  qed\nnext\n  assume P: \"P\"\n  then show \"P \\<or> (P \\<and> Q)\" by simp\nqed\n\nend", "theorem": "or_and_absorb"}, {"problem": "theory MulComm\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\nfun mymul :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"mymul MyZero m = MyZero\" |\n  \"mymul (MySuc n) m = myadd m (mymul n m)\"\n\nlemma myadd_comm: \"myadd n m = myadd m n\"\n  sorry\n\nlemma mymul_0_r: \"mymul n MyZero = MyZero\"\n  sorry\n\nlemma mymul_succ_r: \"mymul n (MySuc m) = myadd (mymul n m) n\"\n  sorry\n\ntheorem mul_comm: \"mymul n m = mymul m n\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction n)\n  case <PERSON><PERSON><PERSON>\n  then show ?case\n    using mymul_0_r by simp\nnext\n  case (MySuc n)\n  then show ?case\n    using mymul_succ_r myadd_comm by (simp add: mymul_0_r mymul_succ_r)\nqed\n\nend", "theorem": "mul_comm"}, {"problem": "theory IterS\n  imports Main\nbegin\n\nfun iter :: \"nat \\<Rightarrow> ('a \\<Rightarrow> 'a) \\<Rightarrow> 'a \\<Rightarrow> 'a\" where\n  \"iter 0 f x = x\" |\n  \"iter (Suc n) f x = f (iter n f x)\"\n\ntheorem iter_S: \"iter n f (f x) = iter (Suc n) f x\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction n)\n  case 0\n  then show ?case by simp\nnext\n  case (Suc n)\n  then show ?case by simp\nqed\n\nend", "theorem": "iter_S"}, {"problem": "theory MulSuccR\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\nfun mymul :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"mymul MyZero m = MyZero\" |\n  \"mymul (MySuc n) m = myadd m (mymul n m)\"\n\nlemma myadd_succ_r: \"myadd n (MySuc m) = MySuc (myadd n m)\"\n  by (induction n; simp)\n\ntheorem myadd_assoc: \"myadd n (myadd m p) = myadd (myadd n m) p\"\n  by (induction n; simp)\n\nlemma mul_succ_r: \"mymul n (MySuc m) = myadd (mymul n m) n\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction n)\n  case MyZero\n  then show ?case by simp\nnext\n  case (MySuc n)\n  then show ?case \n    using myadd_succ_r myadd_assoc by simp\nqed\n\nend", "theorem": "mul_succ_r"}, {"problem": "theory TreeReverseInj\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\nfun tree_reverse :: \"tree \\<Rightarrow> tree\" where\n  \"tree_reverse Leaf = Leaf\"\n| \"tree_reverse (Node l v r) = Node (tree_reverse r) v (tree_reverse l)\"\n\nlemma tree_reverse_inj: \"tree_reverse t1 = tree_reverse t2 \\<Longrightarrow> t1 = t2\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction t1 arbitrary: t2)\n  case Leaf\n  then have \"tree_reverse t2 = Leaf\" by simp\n  thus ?case\n  proof (cases t2)\n    case Leaf\n    then show ?thesis by simp\n  next\n    case (Node l v r)\n    then have \"tree_reverse t2 = Node (tree_reverse r) v (tree_reverse l)\" by simp\n    with `tree_reverse t2 = Leaf` show ?thesis by simp\n  qed\nnext\n  case (Node l1 v1 r1)\n  then obtain t2_l t2_v t2_r where t2_eq: \"t2 = Node t2_l t2_v t2_r \\<or> t2 = Leaf\"\n    by (cases t2) auto\n  then show ?case\n  proof\n    assume \"t2 = Leaf\"\n    with Node.prems have \"tree_reverse t2 = Leaf\" by simp\n    then have \"Node (tree_reverse r1) v1 (tree_reverse l1) = Leaf\" using Node.prems by simp\n    thus ?thesis by simp\n  next\n    assume t2_is_node: \"t2 = Node t2_l t2_v t2_r\"\n    with Node.prems have eq_nodes: \"Node (tree_reverse r1) v1 (tree_reverse l1) = Node (tree_reverse t2_r) t2_v (tree_reverse t2_l)\" by simp\n    then have eq_vals: \"v1 = t2_v\" and eq_left: \"tree_reverse l1 = tree_reverse t2_l\" and eq_right: \"tree_reverse r1 = tree_reverse t2_r\" by auto\n    from Node.IH[OF eq_right] have \"r1 = t2_r\" by blast\n    from Node.IH[OF eq_left] have \"l1 = t2_l\" by blast\n    with `v1 = t2_v` `r1 = t2_r` t2_is_node show ?thesis by simp\n  qed\nqed\n\nend", "theorem": "tree_reverse_inj"}, {"problem": "theory PlusOneMono\n  imports Main\nbegin\n\ndefinition plus_one :: \"int \\<Rightarrow> int\" where\n\"plus_one x = x + 1\"\n\ndefinition mono :: \"(int \\<Rightarrow> int) \\<Rightarrow> bool\" where\n\"mono f = (\\<forall>n m. n \\<le> m \\<longrightarrow> f n \\<le> f m)\"\n\nlemma plus_one_mono: \"mono plus_one\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding mono_def plus_one_def\n  by (simp add: add_mono)  (* Simplify using the additive monotonicity rule *)\n\nend", "theorem": "plus_one_mono"}, {"problem": "theory SmulEx1\n  imports Main\nbegin\n\ndefinition smul :: \"int \\<Rightarrow> int \\<Rightarrow> int\" where\n\"smul x y = x * y + x + y\"\n\nlemma smul_ex1: \"smul 1 1 = 3\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding smul_def\n  by simp\n\nend", "theorem": "smul_ex1"}, {"problem": "theory Mul1L\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\nfun mymul :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"mymul MyZero m = MyZero\" |\n  \"mymul (MySuc n) m = myadd m (mymul n m)\"\n\nlemma myadd_0_r: \"myadd n MyZero = n\"\n  sorry\n\ntheorem mul_1_l: \"mymul (MySuc MyZero) n = n\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  have \"mymul (MySuc MyZero) n = myadd n (mymul MyZero n)\" by simp\n  also have \"... = myadd n MyZero\" by simp\n  also have \"... = n\" using myadd_0_r by simp\n  finally show ?thesis by assumption\nqed\n\nend", "theorem": "mul_1_l"}, {"problem": "theory LeafNodeConflict\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\nlemma Leaf_Node_conflict:\n  assumes \"Leaf = Node l v r\"\n  shows \"1 = 2\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  have \"Leaf \\<noteq> Node l v r\" by simp\n  with assms have False by contradiction\n  thus \"1 = 2\" by simp\nqed\n\nend", "theorem": "Leaf_Node_conflict"}, {"problem": "theory NodeInjValue\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\nlemma Node_inj_value: \n  assumes \"Node l1 v1 r1 = Node l2 v2 r2\"\n  shows \"v1 = v2\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms have \"l1 = l2 \\<and> v1 = v2 \\<and> r1 = r2\"\n    by (cases \"Node l1 v1 r1\", cases \"Node l2 v2 r2\", auto)\n  thus ?thesis by simp\nqed\n\nend", "theorem": "Node_inj_value"}, {"problem": "theory OrImply\n  imports Main\nbegin\n\ntheorem or_imply: \"(P \\<or> Q \\<longrightarrow> R) \\<longleftrightarrow> (P \\<longrightarrow> R) \\<and> (Q \\<longrightarrow> R)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  assume pq_implies_r: \"P \\<or> Q \\<longrightarrow> R\"\n  show \"(P \\<longrightarrow> R) \\<and> (Q \\<longrightarrow> R)\"\n  proof\n    show \"P \\<longrightarrow> R\"\n    proof\n      assume P\n      then have \"P \\<or> Q\" ..\n      from pq_implies_r show R by (rule mp)\n    qed\n    show \"Q \\<longrightarrow> R\"\n    proof\n      assume Q\n      then have \"P \\<or> Q\" ..\n      from pq_implies_r show R by (rule mp)\n    qed\n  qed\nnext\n  assume conj_pr_qr: \"(P \\<longrightarrow> R) \\<and> (Q \\<longrightarrow> R)\"\n  show \"P \\<or> Q \\<longrightarrow> R\"\n  proof\n    assume \"P \\<or> Q\"\n    from conj_pr_qr have pr: \"P \\<longrightarrow> R\" and qr: \"Q \\<longrightarrow> R\" by simp\n    from `P \\<or> Q` show R\n    proof\n      assume P\n      from pr show R by (rule mp)\n    next\n      assume Q\n      from qr show R by (rule mp)\n    qed\n  qed\nqed\n\nend", "theorem": "or_imply"}, {"problem": "theory FourIsEven\n  imports Main\nbegin\n\nlemma four_is_even: \"\\<exists>n. 4 = n + n\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  have \"4 = 2 + 2\" by simp\n  thus ?thesis by blast\nqed\n\nend", "theorem": "four_is_even"}, {"problem": "theory TreeExample0TreeReverse\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\ndefinition tree_example0 :: tree where\n\"tree_example0 = Node Leaf 1 Leaf\"\n\nfun tree_reverse :: \"tree \\<Rightarrow> tree\" where\n\"tree_reverse Leaf = Leaf\" |\n\"tree_reverse (Node l v r) = Node (tree_reverse r) v (tree_reverse l)\"\n\nlemma tree_example0_tree_reverse: \"tree_reverse tree_example0 = tree_example0\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  show ?thesis\n    unfolding tree_example0_def\n    by simp\nqed\n\nend", "theorem": "tree_example0_tree_reverse"}, {"problem": "theory DistExistsAnd\n  imports Main\nbegin\n\ntheorem dist_exists_and:\n  assumes \"\\<exists>x. P x \\<and> Q x\"\n  shows \"(\\<exists>x. P x) \\<and> (\\<exists>x. Q x)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms obtain x where \"P x \\<and> Q x\" by auto\n  then have \"P x\" and \"Q x\" by auto+\n  then show ?thesis by auto\nqed\n\nend", "theorem": "dist_exists_and"}, {"problem": "theory OrComm\n  imports Main\nbegin\n\ntheorem or_comm:\n  assumes \"P \\<or> Q\"\n  shows \"Q \\<or> P\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms show ?thesis\n  proof\n    assume \"P\"\n    then show \"Q \\<or> P\" by (rule disjI2)\n  next\n    assume \"Q\"\n    then show \"Q \\<or> P\" by (rule disjI1)\n  qed\nqed\n\nend", "theorem": "or_comm"}, {"problem": "theory SmulEx2\n  imports Main\nbegin\n\ndefinition smul :: \"int \\<Rightarrow> int \\<Rightarrow> int\" where\n\"smul x y = x * y + x + y\"\n\nlemma smul_ex2: \"smul 2 3 = 11\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding smul_def\n  by simp\n\nend", "theorem": "smul_ex2"}, {"problem": "theory NodeInjLeft\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\nlemma Node_inj_left: \n  assumes \"Node l1 v1 r1 = Node l2 v2 r2\"\n  shows \"l1 = l2\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms have \"l1 = l2 \\<and> v1 = v2 \\<and> r1 = r2\"\n    by (cases \"Node l1 v1 r1\", cases \"Node l2 v2 r2\", auto)\n  thus ?thesis by simp\nqed\n\nend", "theorem": "Node_inj_left"}, {"problem": "theory ModusPonens\n  imports Main\nbegin\n\nlemma modus_ponens: \"P \\<longrightarrow> Q \\<Longrightarrow> P \\<Longrightarrow> Q\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  assume a1: \"P \\<longrightarrow> Q\"\n  assume a2: \"P\"\n  from a1 a2 show \"Q\" by (rule mp)\nqed\n\nend", "theorem": "modus_ponens"}, {"problem": "theory AndCongr\n  imports Main\nbegin\n\nlemma and_congr:\n  \"((P1 \\<longleftrightarrow> P2) \\<and> (Q1 \\<longleftrightarrow> Q2)) \\<longrightarrow> (P1 \\<and> Q1 \\<longleftrightarrow> P2 \\<and> Q2)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  assume eqs: \"(P1 \\<longleftrightarrow> P2) \\<and> (Q1 \\<longleftrightarrow> Q2)\"\n  then have P_eq: \"P1 \\<longleftrightarrow> P2\" and Q_eq: \"Q1 \\<longleftrightarrow> Q2\" by simp_all\n  show \"P1 \\<and> Q1 \\<longleftrightarrow> P2 \\<and> Q2\"\n  proof\n    assume \"P1 \\<and> Q1\"\n    then have P1: \"P1\" and Q1: \"Q1\" by simp_all\n    from P_eq P1 have P2: \"P2\" by (simp add: iffD1)\n    from Q_eq Q1 have Q2: \"Q2\" by (simp add: iffD1)\n    from P2 Q2 show \"P2 \\<and> Q2\" by simp\n  next\n    assume \"P2 \\<and> Q2\"\n    then have P2: \"P2\" and Q2: \"Q2\" by simp_all\n    from P_eq P2 have P1: \"P1\" by (simp add: iffD2)\n    from Q_eq Q2 have Q1: \"Q1\" by (simp add: iffD2)\n    from P1 Q1 show \"P1 \\<and> Q1\" by simp\n  qed\nqed\n\nend", "theorem": "and_congr"}, {"problem": "theory ShiftLeft1FuncPlus\n  imports Main\nbegin\n\ndefinition func_plus :: \"(int \\<Rightarrow> int) \\<Rightarrow> (int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n\"func_plus f g x = f x + g x\"\n                                   \ndefinition shift_left1 :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n\"shift_left1 f x = f (x + 1)\"\n\nlemma shift_left1_func_plus: \"shift_left1 (func_plus f g) = func_plus (shift_left1 f) (shift_left1 g)\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding shift_left1_def func_plus_def\n  by auto\n\nend", "theorem": "shift_left1_func_plus"}, {"problem": "theory ForallEx2\n  imports Main\nbegin\n\nlemma forall_ex2:\n  assumes \"\\<forall>x. P x \\<and> Q x \\<longrightarrow> R x\"\n  shows \"\\<forall>x. P x \\<longrightarrow> Q x \\<longrightarrow> R x\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (rule allI, rule impI, rule impI)\n  fix x\n  assume \"P x\" and \"Q x\"\n  from assms have \"\\<forall>x. P x \\<and> Q x \\<longrightarrow> R x\" by simp\n  then have \"P x \\<and> Q x \\<longrightarrow> R x\" by simp\n  from `P x` and `Q x` have \"P x \\<and> Q x\" by simp\n  with `P x \\<and> Q x \\<longrightarrow> R x` show \"R x\" by simp\nqed\n\nend", "theorem": "forall_ex2"}, {"problem": "theory MultAssoc\n  imports Main\nbegin\n\n(* 定义关联性 *)\ndefinition assoc :: \"(int \\<Rightarrow> int \\<Rightarrow> int) \\<Rightarrow> bool\" where\n  \"assoc f \\<equiv> \\<forall> x y z. f x (f y z) = f (f x y) z\"\n\n(* 证明乘法的关联性 *)\nlemma mult_assoc: \"assoc (\\<lambda>x y. x * y)\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding assoc_def by auto\n\nend", "theorem": "mult_assoc"}, {"problem": "theory AndAssoc1\n  imports Main\nbegin\n\ntheorem and_assoc1: \n  assumes \"P \\<and> (Q \\<and> R)\"\n  shows \"(P \\<and> Q) \\<and> R\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms obtain HP and HQ and HR where \"P\" and \"Q\" and \"R\" by auto\n  then show ?thesis by auto\nqed\n\nend", "theorem": "and_assoc1"}, {"problem": "theory LeafTreeReverse\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\nfun tree_reverse :: \"tree \\<Rightarrow> tree\" where\n\"tree_reverse Leaf = Leaf\" |\n\"tree_reverse (Node l v r) = Node (tree_reverse r) v (tree_reverse l)\"\n\nlemma Leaf_tree_reverse: \"tree_reverse Leaf = Leaf\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  show ?thesis by simp\nqed\n\nend", "theorem": "Leaf_tree_reverse"}, {"problem": "theory AddAssoc\nimports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\ntheorem add_assoc: \"myadd n (myadd m p) = myadd (myadd n m) p\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction n)\n  case <PERSON><PERSON><PERSON>\n  then show ?case by simp\nnext\n  case (MySuc n)\n  then show ?case by simp\nqed\n\nend", "theorem": "add_assoc"}, {"problem": "theory ReverseSize\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\nfun tree_reverse :: \"tree \\<Rightarrow> tree\" where\n  \"tree_reverse Leaf = Leaf\"\n| \"tree_reverse (Node l v r) = Node (tree_reverse r) v (tree_reverse l)\"\n\nfun tree_size :: \"tree \\<Rightarrow> int\" where\n  \"tree_size Leaf = 0\"\n| \"tree_size (Node l v r) = tree_size l + tree_size r + 1\"\n\nlemma reverse_size: \"tree_size (tree_reverse t) = tree_size t\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induct t arbitrary: l v r)\n  case Leaf\n  then show ?case by simp\nnext\n  case (Node l v r)\n  (* Induction hypotheses:\n     Node.IH: \n       1. tree_size (tree_reverse l) = tree_size l\n       2. tree_size (tree_reverse r) = tree_size r\n  *)\n  from Node have IHl: \"tree_size (tree_reverse l) = tree_size l\"\n    using Node.IH(1) by simp\n  from Node have IHr: \"tree_size (tree_reverse r) = tree_size r\"\n    using Node.IH(2) by simp\n  have \"tree_size (tree_reverse (Node l v r)) = tree_size (Node (tree_reverse r) v (tree_reverse l))\"\n    by simp\n  also have \"... = tree_size (tree_reverse r) + tree_size (tree_reverse l) + 1\"\n    by simp\n  also have \"... = tree_size r + tree_size l + 1\"\n    using IHl IHr by simp\n  also have \"... = tree_size l + tree_size r + 1\"\n    by (simp add: add.commute)\n  also have \"... = tree_size (Node l v r)\"\n    by simp\n  finally show ?case .\nqed\n\nend", "theorem": "reverse_size"}, {"problem": "theory ShiftLeft1Square\n  imports Main\nbegin\n\ndefinition square :: \"int \\<Rightarrow> int\" where\n\"square x = x * x\"\n\ndefinition shift_left1 :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n\"shift_left1 f x = f (x + 1)\"\n\nlemma shift_left1_square: \"shift_left1 square x = (x + 1) * (x + 1)\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding shift_left1_def square_def\n  by simp\n\nend", "theorem": "shift_left1_square"}, {"problem": "theory Mul0R\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\nfun mymul :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"mymul MyZero m = MyZero\" |\n  \"mymul (MySuc n) m = myadd m (mymul n m)\"\n\nlemma mul_0_r: \"mymul n MyZero = MyZero\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction n)\n  case <PERSON><PERSON><PERSON>\n  then show ?case by simp\nnext\n  case (MySuc n)\n  then show ?case by simp\nqed\n\nend", "theorem": "mul_0_r"}, {"problem": "theory ForallEx1\n  imports Main\nbegin\n\nlemma forall_ex1:\n  assumes \"\\<And>x. P x \\<longrightarrow> Q x \\<longrightarrow> R x\"\n  shows \"\\<And>x. P x \\<and> Q x \\<longrightarrow> R x\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  fix x\n  assume conj: \"P x \\<and> Q x\"\n  then have px: \"P x\" by simp\n  from conj have qx: \"Q x\" by simp\n  from assms have impl: \"P x \\<longrightarrow> Q x \\<longrightarrow> R x\" by simp\n  from px qx impl show \"R x\" by simp\nqed\n\nend", "theorem": "forall_ex1"}, {"problem": "theory AndAssoc2\n  imports Main\nbegin\n\ntheorem and_assoc2:\n  assumes \"(P \\<and> Q) \\<and> R\"\n  shows \"P \\<and> (Q \\<and> R)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms obtain HP and HQ and HR where \"P\" and \"Q\" and \"R\" by auto\n  then show ?thesis by auto\nqed\n\nend", "theorem": "and_assoc2"}, {"problem": "theory NotAndIff\n  imports Main\nbegin\n\ntheorem not_and_iff: \"\\<not> (P \\<and> Q) \\<longleftrightarrow> (\\<not> P \\<or> \\<not> Q)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  show \"\\<not> (P \\<and> Q) \\<longrightarrow> (\\<not> P \\<or> \\<not> Q)\"\n  proof\n    assume \"\\<not> (P \\<and> Q)\"\n    show \"\\<not> P \\<or> \\<not> Q\"\n    proof (rule classical)\n      assume \"\\<not> (\\<not> P \\<or> \\<not> Q)\"\n      hence \"P \\<and> Q\" by (metis (no_types, lifting) classical)\n      thus False using `\\<not> (P \\<and> Q)` by contradiction\n    qed\n  qed\nnext\n  show \"(\\<not> P \\<or> \\<not> Q) \\<longrightarrow> \\<not> (P \\<and> Q)\"\n  proof\n    assume \"\\<not> P \\<or> \\<not> Q\"\n    show \"\\<not> (P \\<and> Q)\"\n    proof\n      assume \"P \\<and> Q\"\n      then obtain P and Q by auto\n      thus False using `\\<not> P \\<or> \\<not> Q` by auto\n    qed\n  qed\nqed\n\nend", "theorem": "not_and_iff"}, {"problem": "theory OrDup\n  imports Main\nbegin\n\nlemma or_dup: \"P \\<or> P \\<longleftrightarrow> P\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  show \"P \\<or> P \\<Longrightarrow> P\" by (erule disjE, assumption+)\nnext\n  show \"P \\<Longrightarrow> P \\<or> P\" by (rule disjI1)\nqed\n\nend", "theorem": "or_dup"}, {"problem": "theory MonoFuncPlus\n  imports Main\nbegin\n\ndefinition mono :: \"(int \\<Rightarrow> int) \\<Rightarrow> bool\" where\n  \"mono f \\<equiv> \\<forall> n m. n \\<le> m \\<longrightarrow> f n \\<le> f m\"\n\ndefinition func_plus :: \"(int \\<Rightarrow> int) \\<Rightarrow> (int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n  \"func_plus f g \\<equiv> \\<lambda> x. f x + g x\"\n\nlemma mono_func_plus:\n  assumes \"mono f\"\n      and \"mono g\"\n  shows \"mono (func_plus f g)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  have \"mono (\\<lambda> x. f x + g x)\"\n  proof (unfold mono_def, intro allI impI)\n    fix n m :: int\n    assume Hnm: \"n \\<le> m\"\n    from assms(1) have Hf_mono: \"f n \\<le> f m\"\n      unfolding mono_def by (simp add: Hnm)\n    from assms(2) have Hg_mono: \"g n \\<le> g m\"\n      unfolding mono_def by (simp add: Hnm)\n    then have \"f n + g n \\<le> f m + g m\"\n      using Hf_mono Hg_mono by (simp add: add_mono)\n    thus \"f n + g n \\<le> f m + g m\" .\n  qed\n  thus ?thesis\n    unfolding func_plus_def by simp\nqed\n\nend", "theorem": "mono_func_plus"}, {"problem": "theory IffImply\n  imports Main\nbegin\n  \nlemma iff_imply: \"(P \\<longleftrightarrow> Q) \\<Longrightarrow> (P \\<longrightarrow> Q)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  assume H: \"P \\<longleftrightarrow> Q\"\n  show \"P \\<longrightarrow> Q\"\n  proof\n    assume \"P\"\n    from H have \"P \\<Longrightarrow> Q\" by (rule iffD1)\n    then show \"Q\" using \\<open>P\\<close> by (rule mp)\n  qed\nqed\n\nend", "theorem": "iff_imply"}, {"problem": "theory NotForall\n  imports Main\nbegin\n\ntheorem forall_iff:\n  assumes \"\\<forall>x. (P x \\<longleftrightarrow> Q x)\"\n  shows \"(\\<forall>x. P x) \\<longleftrightarrow> (\\<forall>x. Q x)\"\n  using assms by blast\n\ntheorem not_exists:\n  assumes \"\\<not> (\\<exists>x. P x)\"\n  shows \"\\<forall>x. \\<not> P x\"\n  using assms by blast\n\ntheorem not_forall:\n  assumes \"\\<not> (\\<forall>x. P x)\"\n  shows \"\\<exists>x. \\<not> P x\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  have \"(\\<exists>x. \\<not> P x) \\<or> (\\<not> (\\<exists>x. \\<not> P x))\" by (rule classical)\n  thus ?thesis\n  proof\n    assume \"\\<exists>x. \\<not> P x\"\n    thus ?thesis by simp\n  next\n    assume \"\\<not> (\\<exists>x. \\<not> P x)\"\n    then have \"\\<forall>x. P x\"\n      using not_exists by blast\n    then show ?thesis using assms by blast\n  qed\nqed\n\nend", "theorem": "not_forall"}, {"problem": "theory SumOfSqr1\n  imports Main\nbegin\n\nlemma sum_of_sqr1:\n  fixes x y :: int\n  shows \"x * x + y * y \\<ge> x * y\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  have \"x * x + y * y - x * y \\<ge> 0\"\n    by arith\n  thus ?thesis by simp\nqed\n\nend", "theorem": "sum_of_sqr1"}, {"problem": "theory OrAssoc1\n  imports Main\nbegin\n\ntheorem or_assoc1:\n  assumes \"P \\<or> (Q \\<or> R)\"\n  shows \"(P \\<or> Q) \\<or> R\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms show ?thesis\n  proof\n    assume \"P\"\n    then have \"P \\<or> Q\" by (rule disjI1)\n    then show \"(P \\<or> Q) \\<or> R\" by (rule disjI1)\n  next\n    assume \"Q \\<or> R\"\n    then show \"(P \\<or> Q) \\<or> R\"\n    proof\n      assume \"Q\"\n      then have \"P \\<or> Q\" by (rule disjI2)\n      then show \"(P \\<or> Q) \\<or> R\" by (rule disjI1)\n    next\n      assume \"R\"\n      then show \"(P \\<or> Q) \\<or> R\" by (rule disjI2)\n    qed\n  qed\nqed\n\nend", "theorem": "or_assoc1"}, {"problem": "theory Mul1R\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\nfun mymul :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"mymul MyZero m = MyZero\" |\n  \"mymul (MySuc n) m = myadd m (mymul n m)\"\n\ntheorem mymul_comm: \"mymul n m = mymul m n\"\n  sorry\n\ntheorem mymul_1_l: \"mymul (MySuc MyZero) n = n\"\n  sorry\n\ntheorem mul_1_r: \"mymul n (MySuc MyZero) = n\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  have \"mymul n (MySuc MyZero) = mymul (MySuc MyZero) n\" using mymul_comm by simp\n  also have \"... = n\" using mymul_1_l by simp\n  finally show ?thesis by assumption\nqed\n\nend", "theorem": "mul_1_r"}, {"problem": "theory ChickensAndRabbits\n  imports Main\nbegin\n\nlemma chickens_and_rabbits:\n  fixes C R :: int\n  assumes \"C + R = 35\"\n    and \"2 * C + 4 * R = 94\"\n  shows \"C = 23\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms have \"C = 23\"\n    by arith\n  thus ?thesis.\nqed\n\nend", "theorem": "chickens_and_rabbits"}, {"problem": "theory NotExists\n  imports Main\nbegin\n\ntheorem not_exists: \"(\\<not> (\\<exists>x. P x)) \\<Longrightarrow> (\\<forall>x. \\<not> P x)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  assume H: \"\\<not> (\\<exists>x. P x)\"\n  fix x\n  show \"\\<not> P x\"\n  proof\n    assume \"P x\"\n    hence \"\\<exists>x. P x\" by auto\n    thus False using H by auto\n  qed\nqed\n\nend", "theorem": "not_exists"}, {"problem": "theory SumOfSqrLt\n  imports Main\nbegin\n\nlemma sum_of_sqr_lt: \"x < y \\<Longrightarrow> x * x + x * y + y * y > 0\"\n  for x y :: int\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  assume \"x < y\"\n  hence \"(x + y) * (x + y) > 0\"\n    by (metis add_less_mono zero_less_power2)\n  thus \"x * x + x * y + y * y > 0\"\n    using `x < y` by (simp add: power2_eq_square algebra_simps)\nqed\n\nend", "theorem": "sum_of_sqr_lt"}, {"problem": "theory SumOfSqr2\n  imports Main\nbegin\n\nlemma sum_of_sqr2: \"x * x + y * y \\<ge> 2 * x * y\" for x y :: int\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  have \"(x - y) * (x - y) \\<ge> 0\"\n    by (simp add: algebra_simps)\n  then have \"x * x - 2 * x * y + y * y \\<ge> 0\"\n    by (simp add: power2_eq_square algebra_simps)\n  then show ?thesis\n    by (simp add: algebra_simps)\nqed\n\nend", "theorem": "sum_of_sqr2"}, {"problem": "theory OrAssoc2\n  imports Main\nbegin\n\ntheorem or_assoc2:\n  assumes \"((P \\<or> Q) \\<or> R)\"\n  shows \"P \\<or> (Q \\<or> R)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms show ?thesis\n  proof\n    assume \"P \\<or> Q\"\n    then show \"P \\<or> (Q \\<or> R)\"\n    proof\n      assume \"P\"\n      then show \"P \\<or> (Q \\<or> R)\" by (rule disjI1)\n    next\n      assume \"Q\"\n      then have \"Q \\<or> R\" by (rule disjI1)\n      then show \"P \\<or> (Q \\<or> R)\" by (rule disjI2)\n    qed\n  next\n    assume \"R\"\n    then have \"Q \\<or> R\" by (rule disjI2)\n    then show \"P \\<or> (Q \\<or> R)\" by (rule disjI2)\n  qed\nqed\n\nend", "theorem": "or_assoc2"}, {"problem": "theory ExistsExists\n  imports Main\nbegin\n\ntheorem exists_exists_equiv: \n  shows \"((\\<exists>x y. P x y) \\<longleftrightarrow> (\\<exists>y x. P x y))\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  {\n    assume \"\\<exists>x y. P x y\"\n    then obtain x y where \"P x y\" by blast\n    hence \"\\<exists>y x. P x y\" by blast\n  }\n  moreover {\n    assume \"\\<exists>y x. P x y\"\n    then obtain y x where \"P x y\" by blast\n    hence \"\\<exists>x y. P x y\" by blast\n  }\n  ultimately show \"((\\<exists>x y. P x y) \\<longleftrightarrow> (\\<exists>y x. P x y))\" by blast\nqed\n\nend", "theorem": "exists_exists_equiv"}, {"problem": "theory AndOrAbsorb\n  imports Main\nbegin\n\nlemma and_or_absorb:\n  \"P \\<and> (P \\<or> Q) \\<longleftrightarrow> P\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  assume \"P \\<and> (P \\<or> Q)\"\n  then show \"P\" by simp\nnext\n  assume P: \"P\"\n  then have \"P \\<or> Q\" by simp\n  with P show \"P \\<and> (P \\<or> Q)\" by simp\nqed\n\nend", "theorem": "and_or_absorb"}, {"problem": "theory ShiftUp1ShiftLeft1Comm\n  imports Main\nbegin\n\ndefinition shift_left1 :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n\"shift_left1 f x = f (x + 1)\"\n\ndefinition shift_up1 :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n\"shift_up1 f x = f x + 1\"\n\nlemma shift_up1_shift_left1_comm: \"shift_up1 (shift_left1 f) = shift_left1 (shift_up1 f)\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding shift_left1_def shift_up1_def\n  by auto\n\nend", "theorem": "shift_up1_shift_left1_comm"}, {"problem": "theory ReverseHeight\n  imports Main\nbegin\n\ndatatype tree =\n  Leaf\n| Node tree int tree\n\nfun tree_reverse :: \"tree \\<Rightarrow> tree\" where\n  \"tree_reverse Leaf = Leaf\"\n| \"tree_reverse (Node l v r) = Node (tree_reverse r) v (tree_reverse l)\"\n\nfun tree_height :: \"tree \\<Rightarrow> int\" where\n  \"tree_height Leaf = 0\"\n| \"tree_height (Node l v r) = max (tree_height l) (tree_height r) + 1\"\n\nlemma reverse_height: \"tree_height (tree_reverse t) = tree_height t\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction t)\n  case Leaf\n  then show ?case by simp\nnext\n  case (Node l v r)\n  then show ?case by (simp add: Node.IH max.commute)\nqed\n\nend", "theorem": "reverse_height"}, {"problem": "theory IffRefl\n  imports Main\nbegin\n\ntheorem iff_refl: \"P \\<longleftrightarrow> P\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (rule iffI)\n  show \"P \\<Longrightarrow> P\" by assumption\n  show \"P \\<Longrightarrow> P\" by assumption\nqed\n\nend", "theorem": "iff_refl"}, {"problem": "theory SixIsNotPrime\n  imports Main\nbegin\n\nlemma six_is_not_prime: \"\\<exists>n q. 2 \\<le> n \\<and> n < 6 \\<and> n * q = 6\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  show \"\\<exists>n q. 2 \\<le> n \\<and> n < 6 \\<and> n * q = 6\"\n    by (rule exI[of _ 2], rule exI[of _ 3], simp)\nqed\n\nend", "theorem": "six_is_not_prime"}, {"problem": "theory LogicEx8\n  imports Main\nbegin\n\n(* 对应 Coq 中的 logic_ex8 事实 *)\ntheorem logic_ex8:\n  assumes \"\\<forall>a b. P a b \\<longrightarrow> Q a b\"\n  shows \"\\<forall>a b. \\<not> P a b \\<or> Q a b\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (intro allI)\n  fix a b\n  show \"\\<not> P a b \\<or> Q a b\"\n  proof (cases \"P a b\")\n    case True\n    then have \"Q a b\" using assms by blast\n    thus \"\\<not> P a b \\<or> Q a b\" by simp\n  next\n    case False\n    thus \"\\<not> P a b \\<or> Q a b\" by simp\n  qed\nqed\n\nend", "theorem": "logic_ex8"}, {"problem": "theory AndImply\n  imports Main\nbegin\n\nlemma and_imply:\n  \"((P \\<and> Q \\<longrightarrow> R) \\<longleftrightarrow> (P \\<longrightarrow> Q \\<longrightarrow> R))\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  {\n    assume H: \"P \\<and> Q \\<longrightarrow> R\"\n    show \"P \\<longrightarrow> Q \\<longrightarrow> R\"\n    proof (rule impI)+\n      assume P: \"P\" and Q: \"Q\"\n      from H have \"P \\<and> Q \\<longrightarrow> R\" by assumption\n      from this have R: \"R\" using P Q by auto\n      thus \"R\" by assumption\n    qed\n  }\n  {\n    assume H: \"P \\<longrightarrow> Q \\<longrightarrow> R\"\n    show \"P \\<and> Q \\<longrightarrow> R\"\n    proof\n      assume conj: \"P \\<and> Q\"\n      from conj have P: \"P\" and Q: \"Q\" by simp_all\n      from H P Q show \"R\" by simp\n    qed\n  }\nqed\n\nend", "theorem": "and_imply"}, {"problem": "theory NonnegSquare\n  imports Main\nbegin\n\ndefinition square :: \"int \\<Rightarrow> int\" where\n\"square x = x * x\"\n\ndefinition nonneg :: \"int \\<Rightarrow> bool\" where\n\"nonneg x = (x \\<ge> 0)\"\n\nlemma nonneg_square: \"nonneg (square x)\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding nonneg_def square_def\n  using zero_le_power2 by simp\n\nend", "theorem": "nonneg_square"}, {"problem": "theory OrIntror\n  imports Main\nbegin\n\n\nlemma or_intror:\n  fixes A B :: \"bool\"\n  assumes H: \"B\"\n  shows \"A \\<or> B\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  show \"A \\<or> B\" using H by (rule disjI2)\nqed\n\nend", "theorem": "or_intror"}, {"problem": "theory AddComm\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\nlemma myadd_0_r: \"myadd n MyZero = n\"\nproof (induction n)\n  case MyZero\n  then show ?case by simp\nnext\n  case (MySuc n)\n  then show ?case by simp\nqed\n\nlemma myadd_succ_r: \"myadd n (MySuc m) = MySuc (myadd n m)\"\nproof (induction n)\n  case MyZero\n  then show ?case by simp\nnext\n  case (MySuc n)\n  then show ?case by simp\nqed\n\ntheorem add_comm: \"myadd n m = myadd m n\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction n)\n  case <PERSON><PERSON><PERSON>\n  then show ?case\n    using myadd_0_r by simp\nnext\n  case (MySuc n)\n  then have \"myadd n m = myadd m n\" by simp\n  then have \"MySuc (myadd n m) = MySuc (myadd m n)\" by simp\n  then show ?case\n    using myadd_succ_r by simp\nqed\n\nend", "theorem": "add_comm"}, {"problem": "theory OrCongr\n  imports Main\nbegin\n\nlemma or_congr:\n  \"((P1 \\<longleftrightarrow> P2) \\<and> (Q1 \\<longleftrightarrow> Q2)) \\<longrightarrow> (P1 \\<or> Q1 \\<longleftrightarrow> P2 \\<or> Q2)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  assume eqs: \"(P1 \\<longleftrightarrow> P2) \\<and> (Q1 \\<longleftrightarrow> Q2)\"\n  then have P_eq: \"P1 \\<longleftrightarrow> P2\" and Q_eq: \"Q1 \\<longleftrightarrow> Q2\" by simp_all\n  show \"P1 \\<or> Q1 \\<longleftrightarrow> P2 \\<or> Q2\"\n  proof\n    assume \"P1 \\<or> Q1\"\n    then show \"P2 \\<or> Q2\"\n    proof\n      assume \"P1\"\n      then have \"P2\" using P_eq by (simp add: iffD1)\n      thus ?thesis by simp\n    next\n      assume \"Q1\"\n      then have \"Q2\" using Q_eq by (simp add: iffD1)\n      thus ?thesis by simp\n    qed\n  next\n    assume \"P2 \\<or> Q2\"\n    then show \"P1 \\<or> Q1\"\n    proof\n      assume \"P2\"\n      then have \"P1\" using P_eq by (simp add: iffD2)\n      thus ?thesis by simp\n    next\n      assume \"Q2\"\n      then have \"Q1\" using Q_eq by (simp add: iffD2)\n      thus ?thesis by simp\n    qed\n  qed\nqed\n\nend", "theorem": "or_congr"}, {"problem": "theory LogicEx9\n  imports Main\nbegin\n\nlemma logic_ex9:\n  fixes P Q :: \"'a \\<Rightarrow> 'b \\<Rightarrow> bool\"\n  assumes H: \"\\<forall>a b. \\<not> P a b \\<or> Q a b\"\n  shows \"\\<forall>a b. P a b \\<longrightarrow> Q a b\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  {\n    fix a b\n    assume \"P a b\"\n    with H have \"\\<not> P a b \\<or> Q a b\" by auto\n    then have \"Q a b\" by auto\n  }\n  thus ?thesis by auto\nqed\n\nend", "theorem": "logic_ex9"}, {"problem": "theory NotForallImply\n  imports Main\nbegin\n\n(* 对应 Coq 中的 not_imply_iff 定理 *)\ntheorem not_imply_iff: \n  assumes \"\\<not> (P \\<longrightarrow> Q)\"\n  shows \"P \\<and> \\<not> Q\"\n  using assms by auto\n\n(* 对应 Coq 中的 forall_iff 定理 *)\ntheorem forall_iff: \n  assumes \"\\<forall>x. (P x \\<longleftrightarrow> Q x)\"\n  shows \"(\\<forall>x. P x) \\<longleftrightarrow> (\\<forall>x. Q x)\"\n  using assms by blast\n\n(* 对应 Coq 中的 not_exists 定理 *)\ntheorem not_exists: \n  assumes \"\\<not> (\\<exists>x. P x)\"\n  shows \"\\<forall>x. \\<not> P x\"\n  using assms by auto\n\n(* 对应 Coq 中的 not_forall 定理 *)\ntheorem not_forall: \n  assumes \"\\<not> (\\<forall>x. P x)\"\n  shows \"\\<exists>x. \\<not> P x\"\n  using assms by auto\n\n(* 对应 Coq 中的 corollary not_forall_imply *)\ncorollary not_forall_imply: \n  assumes \"\\<not> (\\<forall>x. P x \\<longrightarrow> Q x)\"\n  shows \"\\<exists>x. P x \\<and> \\<not> Q x\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  obtain x where \"\\<not> (P x \\<longrightarrow> Q x)\"\n    using not_forall[of \"\\<lambda>x. P x \\<longrightarrow> Q x\"] assms by auto\n  hence \"P x \\<and> \\<not> Q x\"\n    using not_imply_iff by auto\n  thus ?thesis by blast\nqed\n\nend", "theorem": "not_forall"}, {"problem": "theory ForallForall\n  imports Main\nbegin\n\ntheorem forall_forall:\n  fixes P :: \"'a \\<Rightarrow> 'b \\<Rightarrow> bool\"\n  assumes \"\\<forall>x y. P x y\"\n  shows \"\\<forall>y x. P x y\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms show \"\\<forall>y x. P x y\" \n    by (simp add: assms)\nqed\n\nend", "theorem": "forall_forall"}, {"problem": "theory NotOrIff\n  imports Main\nbegin\n\ntheorem not_or_iff: \n  \"\\<not> (P \\<or> Q) \\<longleftrightarrow> (\\<not> P \\<and> \\<not> Q)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  -- \"证明 \\<not> (P \\<or> Q) \\<Longrightarrow> \\<not> P \\<and> \\<not> Q\"\n  {\n    assume \"\\<not> (P \\<or> Q)\"\n    hence \"\\<not> P\" \n      by (metis disjI1)\n    moreover\n    hence \"\\<not> Q\" \n      by (metis disjI2)\n    ultimately show \"\\<not> P \\<and> \\<not> Q\" \n      by simp\n  }\n\n  -- \"证明 \\<not> P \\<and> \\<not> Q \\<Longrightarrow> \\<not> (P \\<or> Q)\"\n  {\n    assume \"\\<not> P \\<and> \\<not> Q\"\n    then show \"\\<not> (P \\<or> Q)\"\n      by (metis disjI1 disjI2)\n  }\nqed\n\nend", "theorem": "not_or_iff"}, {"problem": "theory ShiftUp1Eq\n  imports Main\nbegin\n\n(* 定义 shift_up1 函数 *)\ndefinition shift_up1 :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n  \"shift_up1 f x \\<equiv> f x + 1\"\n\n(* 定义 func_plus 函数 *)\ndefinition func_plus :: \"(int \\<Rightarrow> int) \\<Rightarrow> (int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n  \"func_plus f g x \\<equiv> f x + g x\"\n\n(* 证明 shift_up1 和 func_plus 的等价性 *)\nlemma shift_up1_eq: \"shift_up1 f = func_plus f (\\<lambda>x. 1)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  (* 展开定义 *)\n  show ?thesis\n    unfolding shift_up1_def func_plus_def\n    (* 使用 auto 证明 *)\n    by auto\nqed\n\nend", "theorem": "shift_up1_eq"}, {"problem": "theory ShiftUp1PlusOne\n  imports Main\nbegin\n\ndefinition plus_one :: \"int \\<Rightarrow> int\" where\n\"plus_one x = x + 1\"\n\ndefinition shift_up1 :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n\"shift_up1 f x = f x + 1\"\n\nlemma shift_up1_plus_one: \"shift_up1 plus_one x = x + 2\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding shift_up1_def plus_one_def\n  by simp\n\nend", "theorem": "shift_up1_plus_one"}, {"problem": "theory TeachersAndChildren\n  imports Main\nbegin\n\nlemma teachers_and_children:\n  fixes MT FT MC FC :: int\n  assumes \"MT > 0\"\n    and \"FT > 0\"\n    and \"MC > 0\"\n    and \"FC > 0\"\n    and \"MT + FT + MC + FC = 16\"\n    and \"MC + FC > MT + FT\"\n    and \"FT > FC\"\n    and \"FC > MC\"\n    and \"MC > MT\"\n  shows \"MT = 1\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms have \"MT = 1\"\n    by arith\n  thus ?thesis.\nqed\n\nend", "theorem": "teachers_and_children"}, {"problem": "theory OnePlusOnePlusOne\n  imports Main\nbegin\n\ndefinition plus_one :: \"int \\<Rightarrow> int\" where\n\"plus_one x = x + 1\"\n\nlemma One_plus_one_plus_one: \"plus_one (plus_one 1) = 3\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding plus_one_def\n  by simp\n\nend", "theorem": "One_plus_one_plus_one"}, {"problem": "theory TreeExample2aHeight\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\ndefinition tree_example2a :: tree where\n\"tree_example2a = Node (Node Leaf 8 Leaf) 100 (Node Leaf 9 Leaf)\"\n\nfun tree_height :: \"tree \\<Rightarrow> int\" where\n\"tree_height Leaf = 0\" |\n\"tree_height (Node l v r) = max (tree_height l) (tree_height r) + 1\"\n\nlemma tree_example2a_height: \"tree_height tree_example2a = 2\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  show ?thesis\n    unfolding tree_example2a_def\n    by simp\nqed\n\nend", "theorem": "tree_example2a_height"}, {"problem": "theory AndComm\n  imports Main\nbegin\n\n\ntheorem and_comm:\n  fixes P Q :: \"bool\"\n  assumes H: \"P \\<and> Q\"\n  shows \"Q \\<and> P\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from H have \"P\" by (rule conjunct1)\n  from H have \"Q\" by (rule conjunct2)\n  show \"Q \\<and> P\" using `Q` `P` by (rule conjI)\nqed\n\nend", "theorem": "and_comm"}, {"problem": "theory NodeInjRight\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\nlemma Node_inj_right: \n  assumes \"Node l1 v1 r1 = Node l2 v2 r2\"\n  shows \"r1 = r2\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  from assms have \"l1 = l2 \\<and> v1 = v2 \\<and> r1 = r2\"\n    by (cases \"Node l1 v1 r1\", cases \"Node l2 v2 r2\", auto)\n  thus ?thesis by simp\nqed\n\nend", "theorem": "Node_inj_right"}, {"problem": "theory QuadEx1\n  imports Main\nbegin\n\nlemma sum_of_sqr1: \"x * x + y * y \\<ge> x * y\" for x y :: int\nproof (rule ccontr)\n  assume \"\\<not> (x * x + y * y \\<ge> x * y)\"\n  then have \"x * x + y * y < x * y\" by simp\n  then show False\n    by l<PERSON><PERSON> (* This would normally be a proof, but we use 'l<PERSON><PERSON>' here to represent a need for a valid argument *)\nqed\n\nlemma quad_ex1: \"x * x + 2 * x * y + y * y + x + y + 1 \\<ge> 0\" for x y :: int\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  have \"sum_of_sqr1 (x + y) (-1)\"\n    by (simp add: sum_of_sqr1)\n  then have \"(x + y) * (x + y) + (-1) * (-1) \\<ge> (x + y) * (-1)\"\n    by (simp add: algebra_simps)\n  then have \"x * x + 2 * x * y + y * y + 1 \\<ge> 0\"\n    by (simp add: algebra_simps)\n  then show ?thesis\n    by simp\nqed\n\nend", "theorem": "quad_ex1"}, {"problem": "theory NotEx2\n  imports Main\nbegin\n\nlemma not_ex2: \"\\<forall>P Q. P \\<longrightarrow> (\\<not> P) \\<longrightarrow> Q\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  {\n    fix P Q\n    assume a1: \"P\"\n    assume a2: \"\\<not> P\"\n    have \"False\" using a1 a2 by auto\n    then have \"Q\" by blast\n  }\n  thus ?thesis by blast\nqed\n\nend", "theorem": "not_ex2"}, {"problem": "theory MulAddDistrL\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\nfun mymul :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"mymul MyZero m = MyZero\" |\n  \"mymul (MySuc n) m = myadd m (mymul n m)\"\n\ntheorem myadd_assoc: \"myadd n (myadd m p) = myadd (myadd n m) p\"\n  by (induction n; simp)\n\ntheorem mymul_add_distr_r: \"mymul (myadd n m) p = myadd (mymul n p) (mymul m p)\"\n  by (induction n; simp add: myadd_assoc)\n\ntheorem mymul_comm: \"mymul n m = mymul m n\"\n  sorry\n\ntheorem mul_add_distr_l: \"mymul n (myadd m p) = myadd (mymul n m) (mymul n p)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  have \"mymul n (myadd m p) = mymul (myadd m p) n\" using mymul_comm by simp\n  also have \"... = myadd (mymul m n) (mymul p n)\" by (simp add: mymul_add_distr_r)\n  also have \"... = myadd (mymul n m) (mymul n p)\" using mymul_comm by simp\n  finally show ?thesis by simp\nqed\n\nend", "theorem": "mul_add_distr_l"}, {"problem": "theory AddCancelL\nimports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\ntheorem add_cancel_l: \"myadd p n = myadd p m \\<longleftrightarrow> n = m\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  show \"myadd p n = myadd p m \\<Longrightarrow> n = m\"\n  proof (induction p)\n    case MyZero\n    then show ?case by simp\n  next\n    case (MySuc p)\n    then have \"myadd p n = myadd p m\" by simp\n    with MySuc.IH show ?case by simp\n  qed\nnext\n  show \"n = m \\<Longrightarrow> myadd p n = myadd p m\"\n    by simp\nqed\n\nend", "theorem": "add_cancel_l"}, {"problem": "theory ReverseResultLeaf\n  imports Main\nbegin\n\ndatatype tree = Leaf | Node tree int tree\n\nfun tree_reverse :: \"tree \\<Rightarrow> tree\" where\n  \"tree_reverse Leaf = Leaf\"\n| \"tree_reverse (Node l v r) = Node (tree_reverse r) v (tree_reverse l)\"\n\nlemma reverse_result_Leaf:\n  assumes \"tree_reverse t = Leaf\"\n  shows \"t = Leaf\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (cases t)\n  case Leaf\n  then show ?thesis by simp\nnext\n  case (Node l v r)\n  then have \"tree_reverse t = Node (tree_reverse r) v (tree_reverse l)\"\n    by simp\n  with assms have \"Node (tree_reverse r) v (tree_reverse l) = Leaf\"\n    by simp\n  then have False\n    by simp  \\<comment> \\<open>Let 'simp' automatically use the distinctness of constructors\\<close>\n  then show ?thesis\n    by simp\nqed\n\n\nend", "theorem": "reverse_result_Leaf"}, {"problem": "theory AddSuccR\n  imports Main\nbegin\n\ndatatype mynat = MyZero (\"0\") | MySuc mynat\n\nfun myadd :: \"mynat \\<Rightarrow> mynat \\<Rightarrow> mynat\" where\n  \"myadd MyZero m = m\" |\n  \"myadd (MySuc n) m = MySuc (myadd n m)\"\n\nlemma add_succ_r: \"myadd n (MySuc m) = MySuc (myadd n m)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof (induction n)\n  case <PERSON><PERSON><PERSON>\n  then show ?case by simp\nnext\n  case (MySuc n)\n  then show ?case by simp\nqed\n\nend", "theorem": "add_succ_r"}, {"problem": "theory NotImplyIff\n  imports Main\nbegin\n\ntheorem not_imply_iff: \"\\<not>(P \\<longrightarrow> Q) \\<longleftrightarrow> (P \\<and> \\<not>Q)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  show \"\\<not>(P \\<longrightarrow> Q) \\<longrightarrow> (P \\<and> \\<not>Q)\"\n  proof\n    assume \"\\<not>(P \\<longrightarrow> Q)\"\n    hence \"P\" \n      by (rule classical)\n    moreover have \"\\<not>Q\"\n    proof\n      assume \"Q\"\n      hence \"P \\<longrightarrow> Q\" by (rule impI)\n      thus False using \\<open>\\<not>(P \\<longrightarrow> Q)\\<close> by contradiction\n    qed\n    ultimately show \"P \\<and> \\<not>Q\" by (rule conjI)\n  qed\nnext\n  show \"(P \\<and> \\<not>Q) \\<longrightarrow> \\<not>(P \\<longrightarrow> Q)\"\n  proof\n    assume \"P \\<and> \\<not>Q\"\n    then have \"P\" and \"\\<not>Q\" by auto\n    assume \"P \\<longrightarrow> Q\"\n    hence \"Q\" using \\<open>P\\<close> by (rule mp)\n    thus False using \\<open>\\<not>Q\\<close> by contradiction\n  qed\nqed\n\nend", "theorem": "not_imply_iff"}, {"problem": "theory AndOrDistrL\n  imports Main\nbegin\n\nlemma and_or_distr_l:\n  \"P \\<and> (Q \\<or> R) \\<longleftrightarrow> (P \\<and> Q) \\<or> (P \\<and> R)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  assume \"P \\<and> (Q \\<or> R)\"\n  then have P: \"P\" and QR: \"Q \\<or> R\"\n    by auto\n  from QR show \"(P \\<and> Q) \\<or> (P \\<and> R)\"\n  proof\n    assume Q: \"Q\"\n    have \"P \\<and> Q\" by (simp add: P Q)\n    thus ?thesis by simp\n  next\n    assume R: \"R\"\n    have \"P \\<and> R\" by (simp add: P R)\n    thus ?thesis by simp\n  qed\nnext\n  assume \"(P \\<and> Q) \\<or> (P \\<and> R)\"\n  then show \"P \\<and> (Q \\<or> R)\"\n  proof\n    assume \"P \\<and> Q\"\n    then have P: \"P\" and Q: \"Q\"\n      by simp_all\n    from P Q have \"Q \\<or> R\" by simp\n    thus ?thesis by (simp add: P)\n  next\n    assume \"P \\<and> R\"\n    then have P: \"P\" and R: \"R\"\n      by simp_all\n    from P R have \"Q \\<or> R\" by simp\n    thus ?thesis by (simp add: P)\n  qed\nqed\n\nend", "theorem": "and_or_distr_l"}, {"problem": "theory NotEx1\n  imports Main\nbegin\n\nlemma not_ex1: \"\\<forall>n m::int. n < m \\<or> \\<not> (n < m)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof -\n  {\n    fix n m :: int\n    have \"n < m \\<or> \\<not> (n < m)\"\n      using classical by auto\n  }\n  thus ?thesis by auto\nqed\n\nend", "theorem": "not_ex1"}, {"problem": "theory MonoPl\n  imports Main\nbegin\n\n(* 定义单调性 *)\ndefinition mono :: \"(int \\<Rightarrow> int) \\<Rightarrow> bool\" where\n  \"mono f \\<equiv> \\<forall>n m. n \\<le> m \\<longrightarrow> f n \\<le> f m\"\n\n(* 定义 shift_left1 函数 *)\ndefinition shift_left1 :: \"(int \\<Rightarrow> int) \\<Rightarrow> int \\<Rightarrow> int\" where\n  \"shift_left1 f x \\<equiv> f (x + 1)\"\n\n(* 定义命题在 shift_left1 下保持 *)\ndefinition preserved_by_shifting_left :: \"((int \\<Rightarrow> int) \\<Rightarrow> bool) \\<Rightarrow> bool\" where\n  \"preserved_by_shifting_left P \\<equiv> \\<forall>f. P f \\<longrightarrow> P (shift_left1 f)\"\n\n(* 证明单调性在 shift_left1 下保持 *)\nlemma mono_pl: \"preserved_by_shifting_left mono\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding preserved_by_shifting_left_def mono_def shift_left1_def\n  by auto\n  \nend", "theorem": "mono_pl"}, {"problem": "theory OnePlusOne\n  imports Main\nbegin\n\ndefinition plus_one :: \"int \\<Rightarrow> int\" where\n\"plus_one x = x + 1\"\n\nlemma One_plus_one: \"plus_one 1 = 2\"\n\n(* Fill Your Proof Here *)\nend", "solution": "  unfolding plus_one_def\n  by simp\n\nend", "theorem": "One_plus_one"}, {"problem": "theory ConstMono\n  imports Main\nbegin\n\n(* 定义单调性 *)\ndefinition mono :: \"(int \\<Rightarrow> int) \\<Rightarrow> bool\" where\n  \"mono f \\<equiv> \\<forall>n m. n \\<le> m \\<longrightarrow> f n \\<le> f m\"\n\n(* 证明常数函数是单调的 *)\nlemma const_mono: \"\\<forall>a. mono (\\<lambda>x. a)\"\n(* Fill Your Proof Here *)\nend", "solution": "proof\n  fix a\n  show \"mono (\\<lambda>x. a)\"\n    unfolding mono_def\n    by auto\nqed\n\nend", "theorem": "const_mono"}]