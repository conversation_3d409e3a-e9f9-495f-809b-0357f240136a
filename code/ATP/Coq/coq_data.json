[{"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Fact chickens_and_rabbits: forall C R: Z,   C + R = 35 ->   2 * C + 4 * R = 94 ->   C = 23.  \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.  lia.  Qed.", "proposition": "chickens_and_rabbits", "id": "./data/1.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Fact teachers_and_children: forall MT FT MC FC: Z,   MT > 0 ->   FT > 0 ->   MC > 0 ->   FC > 0 ->   MT + FT + MC + FC = 16 ->   MC + FC > MT + FT ->   FT > FC ->   FC > MC ->   MC > MT ->   MT = 1.  \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.  lia.  Qed. ", "proposition": "teachers_and_children", "id": "./data/2.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Fact sum_of_sqr1: forall x y: Z,   x * x + y * y >= x * y. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.  nia. Qed.", "proposition": "sum_of_sqr1", "id": "./data/3.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Fact sum_of_sqr2: forall x y: Z,   x * x + y * y >= 2 * x * y. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   pose proof sqr_pos (x - y).   nia. Qed.", "proposition": "sum_of_sqr2", "id": "./data/4.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Fact sum_of_sqr1: forall x y: Z,   x * x + y * y >= x * y.\n Proof.\n nia.\n Qed.\n  Example quad_ex1: forall x y: Z,   x * x + 2 * x * y + y * y + x + y + 1 >= 0. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   pose proof sum_of_sqr1 (x + y) (-1).   nia. Qed.", "proposition": "quad_ex1", "id": "./data/5.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Fact sum_of_sqr_lt: forall x y: Z,   x < y ->   x * x + x * y + y * y > 0.  \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   pose proof sqr_pos (x + y).   nia. Qed. ", "proposition": "sum_of_sqr_lt", "id": "./data/6.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition plus_one (x: Z): Z := x + 1.\n  Example One_plus_one: plus_one 1 = 2. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.  unfold plus_one. lia. Qed.", "proposition": "One_plus_one", "id": "./data/7.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition plus_one (x: Z): Z := x + 1.\n  Example One_plus_one_plus_one: plus_one (plus_one 1) = 3. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.  unfold plus_one. lia. Qed.", "proposition": "One_plus_one_plus_one", "id": "./data/8.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition square (x: Z): Z := x * x.\n  Example square_5: square 5 = 25. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.  unfold square. lia. Qed. ", "proposition": "square_5", "id": "./data/9.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition smul (x y: Z): Z := x * y + x + y.\n  Example smul_ex1: smul 1 1 = 3. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.  unfold smul. lia. Qed.", "proposition": "smul_ex1", "id": "./data/10.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition smul (x y: Z): Z := x * y + x + y.\n  Example smul_ex2: smul 2 3 = 11. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.  unfold smul. lia. Qed.", "proposition": "smul_ex2", "id": "./data/11.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n  Definition square (x: Z): Z := x * x.\n Definition nonneg (x: Z): Prop := x >= 0.\n    Fact nonneg_square: forall x: Z,   nonneg (square x). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.  unfold nonneg, square. nia. Qed.", "proposition": "nonneg_square", "id": "./data/12.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition square (x: Z): Z := x * x.\n Definition shift_left1 (f: Z -> Z) (x: Z): Z :=   f (x + 1).\n  Example shift_left1_square: forall x,   shift_left1 square x = (x + 1) * (x + 1). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.  unfold shift_left1, square. lia. Qed.", "proposition": "shift_left1_square", "id": "./data/13.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition plus_one (x: Z): Z := x + 1.\n Definition shift_left1 (f: Z -> Z) (x: Z): Z :=   f (x + 1).\n    Example shift_left1_plus_one: forall x,   shift_left1 plus_one x = x + 2. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. unfold shift_left1, plus_one. lia. Qed.", "proposition": "shift_left1_plus_one", "id": "./data/14.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n  Definition square (x: Z): Z := x * x.\n  Definition shift_up1 (f: Z -> Z) (x: Z): Z :=   f x + 1.\n  Example shift_up1_square: forall x,   shift_up1 square x = x * x + 1. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. unfold shift_up1, square. lia. Qed.", "proposition": "shift_up1_square", "id": "./data/15.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n  Definition plus_one (x: Z): Z := x + 1.\n  Definition shift_up1 (f: Z -> Z) (x: Z): Z :=   f x + 1.\n    Example shift_up1_plus_one: forall x,   shift_up1 plus_one x = x + 2. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. unfold shift_up1, plus_one. lia. Qed.", "proposition": "shift_up1_plus_one", "id": "./data/16.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n    Definition shift_left1 (f: Z -> Z) (x: Z): Z :=   f (x + 1).\n   Definition shift_up1 (f: Z -> Z) (x: Z): Z :=   f x + 1.\n  Lemma shift_up1_shift_left1_comm: forall f,   shift_up1 (shift_left1 f) = shift_left1 (shift_up1 f). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   unfold shift_left1, shift_up1.   reflexivity. Qed.", "proposition": "shift_up1_shift_left1_comm", "id": "./data/17.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition func_plus (f g: Z -> Z): Z -> Z :=   fun x => f x + g x.\n     Definition shift_left1 (f: Z -> Z) (x: Z): Z :=   f (x + 1).\n  Lemma shift_left1_func_plus: forall f g,   shift_left1 (func_plus f g) =   func_plus (shift_left1 f) (shift_left1 g). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   unfold shift_left1, func_plus.   reflexivity. Qed.", "proposition": "shift_left1_func_plus", "id": "./data/18.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition plus_one (x: Z): Z := x + 1.\n  Definition mono (f: Z -> Z): Prop :=   forall n m, n <= m -> f n <= f m.\n  Example plus_one_mono: mono plus_one. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   unfold mono, plus_one.   intros.   lia. Qed.", "proposition": "plus_one_mono", "id": "./data/19.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n  Definition mono (f: Z -> Z): Prop :=   forall n m, n <= m -> f n <= f m.\n  Definition Zcomp (f g: Z -> Z): Z -> Z :=   fun x => f (g x).\n   Lemma mono_compose: forall f g,   mono f ->   mono g ->   mono (Zcomp f g). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   unfold mono, Zcomp.   intros f g Hf Hg n m Hnm.   pose proof Hg n m Hnm as Hgnm.   pose proof Hf (g n) (g m) Hgnm.   lia. Qed.", "proposition": "mono_compose", "id": "./data/20.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition assoc (f: Z -> Z -> Z): Prop :=   forall x y z,     f x (f y z) = f (f x y) z.\n       Lemma plus_assoc: assoc (fun x y => x + y). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. unfold assoc. lia. Qed.", "proposition": "plus_assoc", "id": "./data/21.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition assoc (f: Z -> Z -> Z): Prop :=   forall x y z,     f x (f y z) = f (f x y) z.\n      Lemma mult_assoc: assoc (fun x y => x * y). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. unfold assoc. nia. Qed.", "proposition": "mult_assoc", "id": "./data/22.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n  Definition mono (f: Z -> Z): Prop :=   forall n m, n <= m -> f n <= f m.\n  Definition shift_up1 (f: Z -> Z) (x: Z): Z :=   f x + 1.\n  Definition preserved_by_shifting_up (P: (Z -> Z) -> Prop): Prop :=   forall f, P f -> P (shift_up1 f).\n  Lemma mono_pu: preserved_by_shifting_up mono. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   unfold preserved_by_shifting_up, mono, shift_up1.   intros.   pose proof H _ _ H0.   lia. Qed.", "proposition": "mono_pu", "id": "./data/23.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition mono (f: Z -> Z): Prop :=   forall n m, n <= m -> f n <= f m.\n  Definition shift_left1 (f: Z -> Z) (x: Z): Z :=   f (x + 1).\n    Definition preserved_by_shifting_left (P: (Z -> Z) -> Prop): Prop :=   forall f, P f -> P (shift_left1 f).\n  Lemma mono_pl: preserved_by_shifting_left mono. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   unfold preserved_by_shifting_left, mono, shift_left1.   intros.   pose proof H (n + 1) (m + 1) ltac:(lia).   lia. Qed.", "proposition": "mono_pl", "id": "./data/24.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition shift_up1 (f: Z -> Z) (x: Z): Z :=   f x + 1.\n Definition func_plus (f g: Z -> Z): Z -> Z :=   fun x => f x + g x.\n  Fact shift_up1_eq: forall f,   shift_up1 f = func_plus f (fun x => 1). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. intros f. unfold shift_up1. unfold func_plus. reflexivity. Qed.", "proposition": "shift_up1_eq", "id": "./data/25.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition mono (f: Z -> Z): Prop :=   forall n m, n <= m -> f n <= f m.\n  Lemma const_mono: forall a: Z,   mono (fun x => a). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. unfold mono. lia. Qed.", "proposition": "const_mono", "id": "./data/26.v"}, {"problem": "Require Import Coq.Setoids.Setoid.\n Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Local Open Scope Z.\n Definition mono (f: Z -> Z): Prop :=   forall n m, n <= m -> f n <= f m.\n  Definition func_plus (f g: Z -> Z): Z -> Z :=   fun x => f x + g x.\n  Lemma mono_func_plus: forall f g,   mono f ->   mono g ->   mono (func_plus f g). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. unfold mono, func_plus. intros f g Hf Hg n m Hnm. pose proof Hg n m Hnm as Hgnm. pose proof Hf n m Hnm as Hfnm. lia. Qed. ", "proposition": "mono_func_plus", "id": "./data/27.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n Definition Zcomp (f g: Z -> Z): Z -> Z :=   fun x => f (g x).\n  Definition is_fixpoint (f: Z -> Z) (x: Z): Prop :=   f x = x.\n   Theorem fixpoint_self_comp: forall f x,   is_fixpoint f x ->   is_fixpoint (Zcomp f f) x. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   unfold is_fixpoint, Zcomp.   intros.   rewrite H.   rewrite H.   reflexivity. Qed.", "proposition": "fixpoint_self_comp", "id": "./data/28.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n Definition Zcomp (f g: Z -> Z): Z -> Z :=   fun x => f (g x).\n  Definition is_fixpoint (f: Z -> Z) (x: Z): Prop :=   f x = x.\n   Example fixpoint_self_comp23: forall f x,   is_fixpoint (Zcomp f f) x ->   is_fixpoint (Zcomp f (Zcomp f f)) x ->   is_fixpoint f x. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   unfold is_fixpoint, Zcomp.   intros.   rewrite H in H0.   rewrite H0.   reflexivity. Qed.", "proposition": "fixpoint_self_comp23", "id": "./data/29.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n     Fixpoint tree_height (t: tree): Z :=   match t with   | Leaf => 0   | Node l v r => Z.max (tree_height l) (tree_height r) + 1   end.\n    Example Leaf_height:   tree_height Leaf = 0. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. reflexivity. Qed.", "proposition": "Leaf_height", "id": "./data/30.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n   Definition tree_example2a: tree :=   Node (Node Leaf 8 Leaf) 100 (Node Leaf 9 Leaf).\n  Fixpoint tree_height (t: tree): Z :=   match t with   | Leaf => 0   | Node l v r => Z.max (tree_height l) (tree_height r) + 1   end.\n    Example tree_example2a_height:   tree_height tree_example2a = 2. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. reflexivity. Qed.", "proposition": "tree_example2a_height", "id": "./data/31.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n  Definition tree_example2b: tree :=   Node (Node Leaf 9 Leaf) 100 (Node Leaf 8 Leaf).\n  Definition tree_example3b: tree :=   Node tree_example2b 5 (Node Leaf 3 Leaf).\n  Fixpoint tree_size (t: tree): Z :=   match t with   | Leaf => 0   | Node l v r => tree_size l + tree_size r + 1   end.\n   Example treeexample3b_size:   tree_size tree_example3b = 5. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. reflexivity. Qed.", "proposition": "treeexample3b_size", "id": "./data/32.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n    Fixpoint tree_reverse (t: tree): tree :=   match t with   | Leaf => Leaf   | Node l v r => Node (tree_reverse r) v (tree_reverse l)   end.\n  Example Leaf_tree_reverse:   tree_reverse Leaf = Leaf. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. reflexivity. Qed.", "proposition": "Leaf_tree_reverse", "id": "./data/33.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n  Definition tree_example0: tree :=   Node Leaf 1 Leaf.\n  Fixpoint tree_reverse (t: tree): tree :=   match t with   | Leaf => Leaf   | Node l v r => Node (tree_reverse r) v (tree_reverse l)   end.\n  Example tree_example0_tree_reverse:   tree_reverse tree_example0 = tree_example0. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. reflexivity. Qed.", "proposition": "tree_example0_tree_reverse", "id": "./data/34.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n  Definition tree_example2a: tree :=   Node (Node Leaf 8 Leaf) 100 (Node Leaf 9 Leaf).\n  Definition tree_example2b: tree :=   Node (Node Leaf 9 Leaf) 100 (Node Leaf 8 Leaf).\n  Definition tree_example3a: tree :=   Node (Node Leaf 3 Leaf) 5 tree_example2a.\n  Definition tree_example3b: tree :=   Node tree_example2b 5 (Node Leaf 3 Leaf).\n  Fixpoint tree_reverse (t: tree): tree :=   match t with   | Leaf => Leaf   | Node l v r => Node (tree_reverse r) v (tree_reverse l)   end.\n  Example tree_example3_tree_reverse:   tree_reverse tree_example3a = tree_example3b. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. reflexivity. Qed. ", "proposition": "tree_example3_tree_reverse", "id": "./data/35.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n    Lemma Node_inj_left: forall l1 v1 r1 l2 v2 r2,   Node l1 v1 r1 = Node l2 v2 r2 ->   l1 = l2. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   injection H as H_l H_v H_r.   rewrite H_l.   reflexivity. Qed.", "proposition": "Node_inj_left", "id": "./data/36.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n    Lemma Node_inj_right: forall l1 v1 r1 l2 v2 r2,   Node l1 v1 r1 = Node l2 v2 r2 ->   r1 = r2. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   injection H as ? ? ?.   apply H1. Qed.", "proposition": "Node_inj_right", "id": "./data/37.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n   Lemma Node_inj_value: forall l1 v1 r1 l2 v2 r2,   Node l1 v1 r1 = Node l2 v2 r2 ->   v1 = v2. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   injection H as _ ? _.   apply H. Qed.", "proposition": "Node_inj_value", "id": "./data/38.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n   Lemma Leaf_Node_conflict: forall l v r,   Leaf = Node l v r -> 1 = 2. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   discriminate. Qed.", "proposition": "Leaf_Node_conflict", "id": "./data/39.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n  Fixpoint tree_reverse (t: tree): tree :=   match t with   | Leaf => Leaf   | Node l v r => Node (tree_reverse r) v (tree_reverse l)   end.\n   Lemma reverse_result_Leaf: forall t,   tree_reverse t = Leaf ->   t = Leaf. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   destruct t.   + reflexivity.   + discriminate <PERSON><PERSON> Qed.", "proposition": "reverse_result_Leaf", "id": "./data/40.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n  Fixpoint tree_reverse (t: tree): tree :=   match t with   | Leaf => Leaf   | Node l v r => Node (tree_reverse r) v (tree_reverse l)   end.\n  Fixpoint tree_size (t: tree): Z :=   match t with   | Leaf => 0   | Node l v r => tree_size l + tree_size r + 1   end.\n   Lemma reverse_size: forall t,   tree_size (tree_reverse t) = tree_size t. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   induction t.   + simpl.     reflexivity.   + simpl.     rewrite IHt1.     rewrite IHt2.     lia. Qed.", "proposition": "reverse_size", "id": "./data/41.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n  Fixpoint tree_reverse (t: tree): tree :=   match t with   | Leaf => Leaf   | Node l v r => Node (tree_reverse r) v (tree_reverse l)   end.\n  Fixpoint tree_height (t: tree): Z :=   match t with   | Leaf => 0   | Node l v r => Z.max (tree_height l) (tree_height r) + 1   end.\n   Lemma reverse_height: forall t,   tree_height (tree_reverse t) = tree_height t. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   induction t.   + simpl.     reflexivity.   + simpl.     rewrite IHt1.     rewrite IHt2.     lia. Qed.", "proposition": "reverse_height", "id": "./data/42.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n  Fixpoint tree_reverse (t: tree): tree :=   match t with   | Leaf => Leaf   | Node l v r => Node (tree_reverse r) v (tree_reverse l)   end.\n  Fixpoint tree_height (t: tree): Z :=   match t with   | Leaf => 0   | Node l v r => Z.max (tree_height l) (tree_height r) + 1   end.\n   Lemma reverse_height_attempt2: forall t,   tree_height (tree_reverse t) = tree_height t. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   induction t; simpl.   + reflexivity.   + simpl.     lia. Qed.", "proposition": "reverse_height_attempt2", "id": "./data/43.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Local Open Scope Z.\n   Inductive tree: Type := | Leaf: tree | Node (l: tree) (v: Z) (r: tree): tree.\n  Fixpoint tree_reverse (t: tree): tree :=   match t with   | Leaf => Leaf   | Node l v r => Node (tree_reverse r) v (tree_reverse l)   end.\n    Lemma tree_reverse_inj: forall t1 t2,   tree_reverse t1 = tree_reverse t2 ->   t1 = t2. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros t1.   induction t1; simpl; intros.   + destruct t2.     - reflexivity.     - discriminate H.   + destruct t2.     - discriminate H.     - injection H as ? ? ?.       rewrite (IHt1_1 _ H1).       rewrite (IHt1_2 _ H).       rewrite H0.       reflexivity. Qed.", "proposition": "tree_reverse_inj", "id": "./data/44.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n   Definition convex (f: Z -> Z): Prop :=   forall x: Z, f (x - 1) + f (x + 1) >= 2 * f x.\n  Definition mono (f: Z -> Z): Prop :=   forall n m, n <= m -> f n <= f m.\n  Fact logic_ex1: forall T: (Z -> Z) -> (Z -> Z),   (forall f, mono f -> mono (T f)) ->   (forall f, convex f -> convex (T f)) ->   (forall f, mono f /\\ convex f -> mono (T f) /\\ convex (T f)).   \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   pose proof H f.   pose proof H0 f.   tauto. Qed.", "proposition": "logic_ex1", "id": "./data/45.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n     Fact logic_ex2: forall P1 Q1 P2 Q2: Prop,   P1 /\\ Q1 ->   (P1 -> P2) ->   (Q1 -> Q2) ->   P2 /\\ Q2. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P1 Q1 P2 Q2 H1 H2 H3.   tauto. Qed.", "proposition": "logic_ex2", "id": "./data/46.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n    Fact logic_ex3: forall {A: Type} (P Q: A -> Prop),   (forall a: A, P a -> Q a) ->   (forall a: A, ~ Q a -> ~ P a). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. intros A P Q H a. pose proof H a. tauto. Qed.", "proposition": "logic_ex3", "id": "./data/47.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Fact logic_ex4: forall {A: Type} (P Q: A -> Prop),   (forall a: A, ~ Q a -> ~ P a) ->   (forall a: A, P a -> Q a). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. intros A P Q H a. pose proof H a. tauto. Qed.", "proposition": "logic_ex4", "id": "./data/48.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n   Lemma and_intro: forall A B: Prop, A -> B -> A /\\ B. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros A B HA HB.   split.   + apply HA.   + apply HB. Qed.", "proposition": "and_intro", "id": "./data/49.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Lemma proj1: forall P Q: Prop,   P /\\ Q -> P. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   destruct H as [HP HQ].   apply HP. Qed.", "proposition": "proj1", "id": "./data/50.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Lemma proj2: forall P Q: Prop,   P /\\ Q -> Q. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   destruct H.   apply H0. Qed.", "proposition": "proj2", "id": "./data/51.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem and_comm: forall P Q: Prop,   P /\\ Q -> Q /\\ P. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   destruct H as [HP HQ].   split.   + apply HQ.   + apply HP. Qed.", "proposition": "and_comm", "id": "./data/52.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Fact or_example:   forall P Q R: Prop, (P -> R) -> (Q -> R) -> (P \\/ Q -> R). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   destruct H1 as [HP | HQ].   + pose proof H HP.     apply H1.   + pose proof H0 HQ.     apply H1. Qed.", "proposition": "or_example", "id": "./data/53.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Lemma or_introl: forall A B: Prop, A -> A \\/ B. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   left.   apply H. Qed.", "proposition": "or_introl", "id": "./data/54.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Lemma or_intror: forall A B: Prop, B -> A \\/ B. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   right.   apply H. Qed.", "proposition": "or_intror", "id": "./data/55.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem iff_refl: forall P: Prop, P <-> P. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   unfold iff.   split.   + intros.     apply H.   + intros.     apply H. Qed.", "proposition": "iff_refl", "id": "./data/56.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem and_dup: forall P: Prop, P /\\ P <-> P. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   split.   + intros.     destruct H.     apply H.   + intros.     split.     - apply H.     - apply H. Qed.", "proposition": "and_dup", "id": "./data/57.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem iff_imply: forall P Q: Prop, (P <-> Q) -> (P -> Q). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q H.   destruct H.   apply H. Qed.", "proposition": "iff_imply", "id": "./data/58.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Lemma four_is_even : exists n, 4 = n + n. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   exists 2.   lia. Qed.", "proposition": "four_is_even", "id": "./data/59.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem dist_exists_and : forall (X: Type) (P Q: X -> Prop),   (exists x, P x /\\ Q x) -> (exists x, P x) /\\ (exists x, Q x). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   destruct H as [x [HP HQ]].   split.   + exists x.     apply HP.   + exists x.     apply HQ. Qed.", "proposition": "dist_exists_and", "id": "./data/60.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Example forall_ex1: forall (X: Type) (P Q R: X -> Prop),   (forall x: X, P x -> Q x -> R x) ->   (forall x: X, P x /\\ Q x -> R x). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros X P Q R H x [HP HQ].   pose proof H x HP HQ.   apply H0. Qed.", "proposition": "forall_ex1", "id": "./data/61.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Example forall_ex2: forall (X: Type) (P Q R: X -> Prop),   (forall x: X, P x /\\ Q x -> R x) ->   (forall x: X, P x -> Q x -> R x). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   specialize (H x ltac:(tauto)).   apply H. Qed.", "proposition": "forall_ex2", "id": "./data/62.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem forall_and: forall (A: Type) (P Q: A -> Prop),   (forall a: A, P a /\\ Q a) <-> (forall a: A, P a) /\\ (forall a: A, Q a). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   split.   + intros.     split.     - intros a.       specialize (H a).       tauto.     - intros a.       specialize (H a).       tauto.   + intros.     destruct H.     specialize (H a).     specialize (H0 a).     tauto. Qed.", "proposition": "forall_and", "id": "./data/63.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Example not_ex1: forall n m: Z, n < m \\/ ~ n < m. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   pose proof classic (n < m).   apply H. Qed.", "proposition": "not_ex1", "id": "./data/64.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Example not_ex2: forall P Q: Prop,   P -> ~ P -> Q. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   tauto. Qed.", "proposition": "not_ex2", "id": "./data/65.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem not_and_iff: forall P Q: Prop,   ~ (P /\\ Q) <-> ~ P \\/ ~ Q. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. intros. tauto. Qed.", "proposition": "not_and_iff", "id": "./data/66.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem not_or_iff: forall P Q: Prop,   ~ (P \\/ Q) <-> ~ P /\\ ~ Q. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. intros. tauto. Qed.", "proposition": "not_or_iff", "id": "./data/67.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem not_imply_iff: forall P Q: Prop,   ~ (P -> Q) <-> P /\\ ~ Q. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. intros. tauto. Qed.", "proposition": "not_imply_iff", "id": "./data/68.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem double_negation_iff: forall P: Prop,   ~ ~ P <-> P. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. intros. tauto. Qed.", "proposition": "double_negation_iff", "id": "./data/69.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem not_exists: forall (X: Type) (P: X -> Prop),   ~ (exists x: X, P x) -> (forall x: X, ~ P x). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   pose proof classic (P x) as [? | ?].   + assert (exists x: X, P x). {       exists x.       apply H0.     }     tauto.   + apply H0. Qed.", "proposition": "not_exists", "id": "./data/70.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem forall_iff : forall (X : Type) (P Q : X -> Prop),   (forall x : X, P x <-> Q x) ->   ((forall x : X, P x) <-> (forall x : X, Q x)). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros X P Q H.   split.   - intros HP x.     specialize (H x).     apply H.     apply HP.   - intros HQ x.     specialize (H x).     apply H.     apply HQ. Qed.", "proposition": "forall_iff", "id": "./data/71.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem forall_iff : forall (X : Type) (P Q : X -> Prop),   (forall x : X, P x <-> Q x) ->   ((forall x : X, P x) <-> (forall x : X, Q x)).\n Admitted.\n  Theorem not_exists: forall (X: Type) (P: X -> Prop),   ~ (exists x: X, P x) -> (forall x: X, ~ P x).\n Admitted.\n  Theorem not_forall: forall (X: Type) (P: X -> Prop),   ~ (forall x: X, P x) -> (exists x: X, ~ P x). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   pose proof classic (exists x: X, ~ P x) as [? | ?].   + tauto.   + pose proof not_exists _ _ H0.     assert (forall x: X, P x <-> ~ ~ P x). {       intros.       tauto.     }     pose proof forall_iff _ P (fun x => ~ ~ P x) H2.     tauto. Qed.  ", "proposition": "not_forall", "id": "./data/72.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Fact logic_ex5: forall {A: Type} (P Q: A -> Prop),   (forall a: A, P a -> Q a) ->   (forall a: A, P a) ->   (forall a: A, Q a). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros A P Q H1 H2.   intro a.   apply H1.   apply H2. Qed.", "proposition": "logic_ex5", "id": "./data/73.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Fact logic_ex6: forall {A: Type} (P Q: A -> Prop) (a0: A),   P a0 ->   (forall a: A, P a -> Q a) ->   Q a0. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros A P Q a0 HP Himp.   apply Himp.   apply HP. Qed. ", "proposition": "logic_ex6", "id": "./data/74.v"}, {"problem": "Fact logic_ex7: forall {A: Type} (P Q: A -> Prop) (a0: A),   (forall a: A, P a -> Q a -> False) ->   Q a0 ->   ~ P a0. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros A P Q a0 Hcontradiction HQ.   unfold not.   intros HP.   apply Hcontradiction with a0.   - apply HP.   - apply HQ. Qed.", "proposition": "logic_ex7", "id": "./data/75.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Fact logic_ex8: forall {A B: Type} (P Q: A -> B -> Prop),   (forall (a: A) (b: B), P a b -> Q a b) ->   (forall (a: A) (b: B), ~ P a b \\/ Q a b). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros A B P Q Himp.   intros a b.   pose proof (classic (P a b)) as [HP | HnP].   - right. apply Himp. assumption.   - left. assumption. Qed. ", "proposition": "logic_ex8", "id": "./data/76.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Fact logic_ex9: forall {A B: Type} (P Q: A -> B -> Prop),   (forall (a: A) (b: B), ~ P a b \\/ Q a b) ->   (forall (a: A) (b: B), P a b -> Q a b). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros A B P Q H.   intros a b HP.   specialize (H a b).   destruct H as [HnP | HQ].   - contradiction.   - assumption. Qed.", "proposition": "logic_ex9", "id": "./data/77.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem and_assoc1: forall P Q R: Prop,   P /\\ (Q /\\ R) -> (P /\\ Q) /\\ R. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q R H.   destruct H as [HP [HQ HR]].   split.   - split.     + exact HP.     + exact HQ.   - exact HR. Qed.", "proposition": "and_assoc1", "id": "./data/78.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem and_assoc2: forall P Q R: Prop,   (P /\\ Q) /\\ R -> P /\\ (Q /\\ R). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q R H.   destruct H as [[HP HQ] HR].   split.   - exact HP.   - split.     + exact HQ.     + exact HR. Qed.", "proposition": "and_assoc2", "id": "./data/79.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem or_comm: forall P Q: Prop,   P \\/ Q -> Q \\/ P. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q H.   destruct H as [HP | HQ].   - right. apply HP.   - left. apply HQ. Qed. ", "proposition": "or_comm", "id": "./data/80.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem or_assoc1: forall P Q R: Prop,   P \\/ (Q \\/ R) -> (P \\/ Q) \\/ R. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q R <PERSON>.   destruct H as [HP | [HQ | HR]].   - left. left. assumption.   - left. right. assumption.   - right. assumption. Qed.", "proposition": "or_assoc1", "id": "./data/81.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem or_assoc2: forall P Q R: Prop,   (P \\/ Q) \\/ R -> P \\/ (Q \\/ R). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q R H.   destruct H as [H_PQ | HR].   - destruct H_PQ as [HP | HQ].     + left. assumption.     + right. left. assumption.   - right. right. assumption. Qed.", "proposition": "or_assoc2", "id": "./data/82.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem or_dup: forall P: Prop, P \\/ P <-> P. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P.   split.   - intros H<PERSON>     destruct H as [HP | HP].     + assumption.     + assumption.   - intros HP.     left.  (* You could also use `right.` here, as both are valid. *)     assumption. Qed.", "proposition": "or_dup", "id": "./data/83.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem modus_ponens: forall P Q: Prop,   P /\\ (P -> Q) -> Q. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q H.   destruct H as [HP HPQ].   apply HPQ.   assumption. Qed.", "proposition": "modus_ponens", "id": "./data/84.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem and_or_distr_l: forall P Q R: Prop,   P /\\ (Q \\/ R) <-> (P /\\ Q) \\/ (P /\\ R). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q R.   split.   - intros [HP HQR].     destruct HQR as [HQ | HR].     + left. split; assumption.     + right. split; assumption.   - intros [[HP HQ] | [HP HR]].     + split.       * assumption.       * left; assumption.     + split.       * assumption.       * right; assumption. Qed.", "proposition": "and_or_distr_l", "id": "./data/85.v"}, {"problem": "Theorem or_and_distr_l: forall P Q R: Prop,   P \\/ (Q /\\ R) <-> (P \\/ Q) /\\ (P \\/ R). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q R.   split.   - intros [HP | [HQ HR]].     + split; left; assumption.     + split; right; [apply HQ | apply HR].   - intros [[HP | HQ] [HP' | HR]].     + left; assumption.     + left; assumption.     + left; assumption.     + right; split; [apply HQ | apply HR]. Qed.", "proposition": "or_and_distr_l", "id": "./data/86.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem and_or_absorb: forall P Q: Prop,   P /\\ (P \\/ Q) <-> P. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q.   split.   - intros [HP _].     exact HP.   - intros HP.     split.     + assumption.     + left. assumption. Qed.", "proposition": "and_or_absorb", "id": "./data/87.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem or_and_absorb: forall P Q: Prop,   P \\/ (P /\\ Q) <-> P. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q.   split.   - intros [HP | [HP _]].     + exact HP.     + exact HP.   - intros HP.     left.      exact HP. Qed.", "proposition": "or_and_absorb", "id": "./data/88.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem and_congr: forall P1 Q1 P2 Q2: Prop,   (P1 <-> P2) ->   (Q1 <-> Q2) ->   (P1 /\\ Q1 <-> P2 /\\ Q2). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P1 Q1 P2 Q2 HPQ HQR.   split.   - intros [HP1 HQ1].     split.     + apply HPQ; assumption.     + apply HQR; assumption.   - intros [HP2 HQ2].     split.     + apply HPQ; assumption.     + apply HQR; assumption. Qed. ", "proposition": "and_congr", "id": "./data/89.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem or_congr: forall P1 Q1 P2 Q2: Prop,   (P1 <-> P2) ->   (Q1 <-> Q2) ->   (P1 \\/ Q1 <-> P2 \\/ Q2). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P1 Q1 P2 Q2 HPQ HQR.   split.   - intros [HP1 | HQ1].     + left. apply HPQ. assumption.     + right. apply HQR. assumption.   - intros [HP2 | HQ2].     + left. apply HPQ. assumption.     + right. apply HQR. assumption. Qed. ", "proposition": "or_congr", "id": "./data/90.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n Theorem imply_congr: forall P1 Q1 P2 Q2: Prop,   (P1 <-> P2) ->   (Q1 <-> Q2) ->   ((P1 -> Q1) <-> (P2 -> Q2)). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P1 Q1 P2 Q2 HPQ HQR.   split.   - intros H1 H2.     apply HQR.     apply H1.     apply HPQ.     assumption.   - intros H2 H1.     apply HQR.     apply H2.     apply HPQ.     assumption. Qed. ", "proposition": "imply_congr", "id": "./data/91.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem and_imply: forall P Q R: Prop,   (P /\\ Q -> R) <-> (P -> Q -> R). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q R.   split.   - intros H HP HQ.     apply H.     split; assumption.   - intros H [HP HQ].     apply H; assumption. Qed.", "proposition": "and_imply", "id": "./data/92.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem or_imply: forall P Q R: Prop,   (P \\/ Q -> R) <-> (P -> R) /\\ (Q -> R). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros P Q R.   split.   - intros H.     split.     + intro HP. apply H. left. assumption.     + intro HQ. apply H. right. assumption.   - intros [HP HR] H.     destruct H as [HPQ | HQ].     + apply HP. assumption.     + apply HR. assumption. Qed.", "proposition": "or_imply", "id": "./data/93.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Lemma six_is_not_prime: exists n, 2 <= n < 6 /\\ exists q, n * q = 6. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   exists 2. (* Choose n = 2 *)   split.   - (* Show that 2 <= 2 < 6 *)     split.     + (* 2 <= 2 *)       auto with arith.       reflexivity.     + (* 2 < 6 *)       auto with arith.       reflexivity.   - (* Show that there exists q such that 2 * q = 6 *)     exists 3. (* Choose q = 3 *)     reflexivity. Qed. ", "proposition": "six_is_not_prime", "id": "./data/94.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem exists_exists : forall (X Y : Type) (P : X -> Y -> Prop),   (exists x y, P x y) <-> (exists y x, P x y). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros X Y P.   split.   - intros [x [y Pxy]]. (* Handling the direction (exists x y, P x y) -> (exists y x, P x y) *)     exists y, x.       (* Simply reverse the order of x and y *)     exact Pxy.   - intros [y [x Pxy]]. (* Handling the direction (exists y x, P x y) -> (exists x y, P x y) *)     exists x, y.       (* Reverse the order back from y x to x y *)     exact Pxy. Qed. ", "proposition": "exists_exists", "id": "./data/95.v"}, {"problem": "Require Import Coq.ZArith.ZArith.\n Require Import Coq.micromega.Psatz.\n Require Import Coq.Setoids.Setoid.\n Require Import Coq.Logic.Classical_Prop.\n Local Open Scope Z.\n  Theorem forall_forall : forall (X Y: Type) (P: X -> Y -> Prop),   (forall x y, P x y) -> (forall y x, P x y). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros X Y P H.   intros y x.   apply H. Qed. ", "proposition": "forall_forall", "id": "./data/96.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Lemma add_0_r: forall n, add n O = n. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   induction n; simpl.   + reflexivity.   + rewrite IHn.     reflexivity. Qed.", "proposition": "add_0_r", "id": "./data/97.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Lemma add_succ_r: forall n m,   add n (S m) = S (add n m). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   induction n; simpl.   + reflexivity.   + rewrite IHn.     reflexivity. Qed.", "proposition": "add_succ_r", "id": "./data/98.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Lemma add_0_r: forall n, add n O = n.\n Admitted.\n  Lemma add_succ_r: forall n m,   add n (S m) = S (add n m).\n Admitted.\n  Theorem add_comm: forall n m,   add n m = add m n. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   induction n; simpl.   + rewrite add_0_r.     reflexivity.   + rewrite add_succ_r.     rewrite IHn.     reflexivity. Qed.", "proposition": "add_comm", "id": "./data/99.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Theorem add_assoc:   forall n m p, add n (add m p) = add (add n m) p. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros n m p; induction n; simpl.   + reflexivity.   + simpl.     rewrite IHn.     reflexivity. Qed.", "proposition": "add_assoc", "id": "./data/100.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Theorem add_cancel_l:   forall n m p, add p n = add p m <-> n = m. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros n m p; split.   + induction p; simpl; intros H.     - tauto.     - injection H as H.       pose proof IHp H.       tauto.   + intros H.     rewrite H.     reflexivity. Qed.", "proposition": "add_cancel_l", "id": "./data/101.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Theorem add_comm: forall n m,   add n m = add m n.\n Proof.\n Admitted.\n   Theorem add_cancel_l:   forall n m p, add p n = add p m <-> n = m.\n Proof.\n Admitted.\n  Theorem add_cancel_r:   forall n m p, add n p = add m p <-> n = m. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros n m p.   rewrite (add_comm n p), (add_comm m p).   apply add_cancel_l. Qed.", "proposition": "add_cancel_r", "id": "./data/102.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Fixpoint mul (n m: nat): nat :=   match n with   | O => O   | S p => add m (mul p m)   end.\n  Lemma mul_0_r: forall n, mul n O = O. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   induction n; simpl.   + reflexivity.   + apply IHn. Qed.", "proposition": "mul_0_r", "id": "./data/103.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Fixpoint mul (n m: nat): nat :=   match n with   | O => O   | S p => add m (mul p m)   end.\n  Lemma add_succ_r: forall n m,   add n (S m) = S (add n m).\n Proof.\n Admitted.\n  Theorem add_assoc:   forall n m p, add n (add m p) = add (add n m) p.\n Proof.\n Admitted.\n  Lemma mul_succ_r:   forall n m, mul n (S m) = add (mul n m) n. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros n m; induction n; simpl.   + reflexivity.   + rewrite IHn, add_succ_r.     rewrite <- add_assoc.     reflexivity. Qed.", "proposition": "mul_succ_r", "id": "./data/104.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Fixpoint mul (n m: nat): nat :=   match n with   | O => O   | S p => add m (mul p m)   end.\n  Theorem add_comm: forall n m,   add n m = add m n.\n Proof.\n Admitted.\n  Lemma mul_0_r: forall n, mul n O = O.\n Proof.\n Admitted.\n  Lemma mul_succ_r:   forall n m, mul n (S m) = add (mul n m) n.\n Proof.\n Admitted.\n  Theorem mul_comm:   forall n m, mul n m = mul m n. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros n m; induction n; simpl.   + rewrite mul_0_r.     reflexivity.   + rewrite mul_succ_r.     rewrite IHn, add_comm.     reflexivity. Qed.", "proposition": "mul_comm", "id": "./data/105.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Fixpoint mul (n m: nat): nat :=   match n with   | O => O   | S p => add m (mul p m)   end.\n  Theorem add_assoc:   forall n m p, add n (add m p) = add (add n m) p.\n Proof.\n Admitted.\n  Theorem mul_add_distr_r:   forall n m p, mul (add n m) p = add (mul n p) (mul m p). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros n m p; induction n; simpl.   - reflexivity.   - rewrite <- add_assoc, IHn.     reflexivity. Qed.", "proposition": "mul_add_distr_r", "id": "./data/106.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Fixpoint mul (n m: nat): nat :=   match n with   | O => O   | S p => add m (mul p m)   end.\n  Theorem mul_comm:   forall n m, mul n m = mul m n.\n Proof.\n Admitted.\n  Theorem mul_add_distr_r:   forall n m p, mul (add n m) p = add (mul n p) (mul m p).\n Proof.\n Admitted.\n  Theorem mul_add_distr_l:   forall n m p, mul n (add m p) = add (mul n m) (mul n p). \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros n m p.   rewrite (mul_comm n (add m p)), (mul_comm n m), (mul_comm n p).   apply mul_add_distr_r. Qed.", "proposition": "mul_add_distr_l", "id": "./data/107.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Fixpoint mul (n m: nat): nat :=   match n with   | O => O   | S p => add m (mul p m)   end.\n   Theorem mul_add_distr_r:   forall n m p, mul (add n m) p = add (mul n p) (mul m p).\n Proof.\n Admitted.\n  Theorem mul_assoc:   forall n m p, mul n (mul m p) = mul (mul n m) p. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros n m p; induction n; simpl.   + reflexivity.   + rewrite IHn, mul_add_distr_r.     reflexivity. Qed.", "proposition": "mul_assoc", "id": "./data/108.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Fixpoint mul (n m: nat): nat :=   match n with   | O => O   | S p => add m (mul p m)   end.\n  Lemma add_0_r: forall n, add n O = n.\n Proof.\n Admitted.\n   Theorem mul_1_l : forall n, mul (S O) n = n. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. intros. simpl. apply add_0_r. Qed.", "proposition": "mul_1_l", "id": "./data/109.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n   Inductive nat := | O: nat | S (n: nat): nat.\n  Fixpoint add (n m: nat): nat :=   match n with   | O => m   | S n' => S (add n' m)   end.\n  Fixpoint mul (n m: nat): nat :=   match n with   | O => O   | S p => add m (mul p m)   end.\n  Theorem mul_comm:   forall n m, mul n m = mul m n.\n Proof.\n Admitted.\n   Theorem mul_1_l : forall n, mul (S O) n = n.\n Proof.\n Admitted.\n  Theorem mul_1_r : forall n, mul n (S O) = n. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof. intros. rewrite mul_comm, mul_1_l. reflexivity. Qed. ", "proposition": "mul_1_r", "id": "./data/110.v"}, {"problem": "Require Import Coq.Arith.PeanoNat.\n  Theorem iter_S: forall {A: Type} (n: nat) (f: A -> A) (x: A),   Nat.iter n f (f x) = Nat.iter (S n) f x. \n(**********)\n(** Fill in your proof here*)\n(**********)", "proof": "Proof.   intros.   induction n; simpl.   + reflexivity.   + rewrite IHn; simpl.     reflexivity. Qed.", "proposition": "iter_S", "id": "./data/111.v"}]