#!/usr/bin/env python3
"""
环境设置脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装 Python 包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ 安装失败: {package}")
        return False

def check_and_install_dependencies():
    """检查并安装依赖"""
    required_packages = [
        "openai>=1.0.0",
        "torch",
        "transformers",
        "numpy",
        "tqdm",
        "requests"
    ]
    
    print("检查并安装依赖包...")
    success_count = 0
    
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装结果: {success_count}/{len(required_packages)} 成功")
    return success_count == len(required_packages)

def check_data_files():
    """检查必要的数据文件"""
    required_files = [
        "./algebra_knowledge.json",
        "./geometry_knowledge.json", 
        "./new_algebra_total.json",
        "./new_geometry_total.json"
    ]
    
    print("\n检查数据文件...")
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ 找到: {file_path}")
        else:
            print(f"❌ 缺失: {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  缺失 {len(missing_files)} 个数据文件")
        print("请确保这些文件存在于项目目录中")
        return False
    else:
        print("\n✅ 所有数据文件都存在")
        return True

def create_directories():
    """创建必要的目录"""
    directories = [
        "./log",
        "./log/new_algebra",
        "./log/new_algebra/gpt-4o-mini-ca",
        "./log/new_algebra/gpt-4o-mini-ca/multi",
        "./log/new_geometry",
        "./log/new_geometry/gpt-4o-mini-ca", 
        "./log/new_geometry/gpt-4o-mini-ca/multi",
        "./user_session_logs",
        "./user_session_logs/new_algebra",
        "./user_session_logs/new_algebra/multi",
        "./user_session_logs/new_algebra/multi/gpt-4o-mini-ca",
        "./user_session_logs/new_geometry",
        "./user_session_logs/new_geometry/multi",
        "./user_session_logs/new_geometry/multi/gpt-4o-mini-ca"
    ]
    
    print("\n创建目录结构...")
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建: {directory}")
        else:
            print(f"📁 已存在: {directory}")

def main():
    print("=" * 60)
    print("Math Agent 环境设置")
    print("=" * 60)
    
    # 1. 安装依赖
    deps_ok = check_and_install_dependencies()
    
    # 2. 检查数据文件
    files_ok = check_data_files()
    
    # 3. 创建目录
    create_directories()
    
    print("\n" + "=" * 60)
    print("设置完成")
    print("=" * 60)
    
    if deps_ok and files_ok:
        print("🎉 环境设置成功!")
        print("\n下一步:")
        print("1. 运行测试: python test_gpt_config.py")
        print("2. 运行 Agent: python run_gpt_agent.py --mode default --category algebra")
    else:
        print("⚠️  环境设置不完整，请解决上述问题")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
