#!/usr/bin/env python3
"""
使用 GPT-4o-mini-ca 运行数学 Agent 的简化脚本
"""

import argparse
import os
import sys

def create_log_directories():
    """创建必要的日志目录"""
    directories = [
        "./log",
        "./log/new_algebra",
        "./log/new_algebra/gpt-4o-mini-ca",
        "./log/new_algebra/gpt-4o-mini-ca/multi",
        "./log/new_geometry", 
        "./log/new_geometry/gpt-4o-mini-ca",
        "./log/new_geometry/gpt-4o-mini-ca/multi",
        "./user_session_logs"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")

def run_single_test(mode, category, start_index=0, original_correct=0):
    """运行单个测试"""
    
    # 创建日志目录
    create_log_directories()
    
    # 构建命令
    cmd = [
        "python", "run_agent.py",
        "--test_model_name", "gpt-4o-mini-ca",
        "--mode"] + mode.split() + [
        "--category", category,
        "--start_index", str(start_index),
        "--original_correct", str(original_correct),
        "--ip", "dummy"  # 不再使用，但保留参数
    ]
    
    print(f"运行命令: {' '.join(cmd)}")
    
    # 运行命令
    import subprocess
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
        if result.returncode == 0:
            print("✅ 测试完成")
            print("输出:", result.stdout[-500:])  # 显示最后500字符
        else:
            print("❌ 测试失败")
            print("错误:", result.stderr)
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时")
    except Exception as e:
        print(f"❌ 运行错误: {e}")

def main():
    parser = argparse.ArgumentParser(description='运行 GPT-4o-mini-ca 数学 Agent')
    parser.add_argument('--mode', type=str, default='default',
                       choices=['default', 'planning', 'reasoning', 'action', 'reflection',
                               'planning reasoning', 'planning action', 'planning reflection',
                               'reasoning action', 'reasoning reflection', 'action reflection',
                               'planning reasoning action', 'planning action reflection',
                               'planning reasoning reflection', 'reasoning action reflection',
                               'planning reasoning action reflection'],
                       help='运行模式')
    parser.add_argument('--category', type=str, default='algebra',
                       choices=['algebra', 'geometry'],
                       help='问题类别')
    parser.add_argument('--start_index', type=int, default=0,
                       help='开始索引')
    parser.add_argument('--original_correct', type=int, default=0,
                       help='之前正确的数量')
    parser.add_argument('--quick_test', action='store_true',
                       help='快速测试模式（只运行几个问题）')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("GPT-4o-mini-ca 数学 Agent 运行器")
    print("=" * 60)
    print(f"模式: {args.mode}")
    print(f"类别: {args.category}")
    print(f"开始索引: {args.start_index}")
    print(f"之前正确数量: {args.original_correct}")
    print("-" * 60)
    
    if args.quick_test:
        print("🚀 快速测试模式")
        # 修改 run_agent.py 以支持限制问题数量
        print("注意: 快速测试模式需要修改 run_agent.py 来限制问题数量")
    
    run_single_test(args.mode, args.category, args.start_index, args.original_correct)

if __name__ == "__main__":
    main()
