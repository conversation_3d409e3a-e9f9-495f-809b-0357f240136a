# GPT-4o-mini-ca 配置和运行指南

## 概述

本指南将帮助您配置和运行使用 GPT-4o-mini-ca 模型的数学问题求解 Agent。

## 配置信息

### API 配置
- **模型名称**: `gpt-4o-mini-ca`
- **API Key**: `sk-9GlGuB7jKnaoifXTdnMZwVTMKLLGhXdScdJ9n4fmuaGRf3bF`
- **Base URL**: `https://api.chatanywhere.tech/v1`

### 已修改的文件
1. `llm_agent.py` - 更新了 APIAgent 类以支持您的 OpenAI 中转服务
2. `config.py` - 新建的配置文件，包含 API 和模型配置
3. `test_gpt_config.py` - 测试脚本，验证 GPT 配置是否正常工作
4. `run_gpt_agent.py` - 简化的运行脚本
5. `setup_environment.py` - 环境设置脚本

## 快速开始

### 1. 环境设置

```bash
# 进入项目目录
cd code/Math

# 运行环境设置脚本
python setup_environment.py
```

### 2. 测试配置

```bash
# 测试 GPT API 连接
python test_gpt_config.py
```

如果看到 "✅ API 连接成功!" 说明配置正确。

### 3. 运行 Agent

#### 基础运行
```bash
# 运行代数问题，默认模式
python run_gpt_agent.py --mode default --category algebra

# 运行几何问题，规划模式
python run_gpt_agent.py --mode planning --category geometry
```

#### 高级运行
```bash
# 多模式组合
python run_gpt_agent.py --mode "planning reasoning action" --category algebra

# 从特定索引开始
python run_gpt_agent.py --mode default --category algebra --start_index 10 --original_correct 5
```

## 运行模式说明

### 单一模式
- `default`: 默认模式
- `planning`: 规划模式
- `reasoning`: 推理模式  
- `action`: 行动模式
- `reflection`: 反思模式

### 组合模式
- `planning reasoning`: 规划+推理
- `planning action`: 规划+行动
- `planning reflection`: 规划+反思
- `reasoning action`: 推理+行动
- `reasoning reflection`: 推理+反思
- `action reflection`: 行动+反思
- `planning reasoning action`: 规划+推理+行动
- `planning action reflection`: 规划+行动+反思
- `planning reasoning reflection`: 规划+推理+反思
- `reasoning action reflection`: 推理+行动+反思
- `planning reasoning action reflection`: 全模式

## 目录结构

```
code/Math/
├── config.py                    # 配置文件
├── llm_agent.py                 # Agent 实现（已修改）
├── test_gpt_config.py          # 测试脚本
├── run_gpt_agent.py            # 简化运行脚本
├── setup_environment.py        # 环境设置
├── run_agent.py                # 原始运行脚本
├── log/                        # 日志目录
│   ├── new_algebra/
│   │   └── gpt-4o-mini-ca/
│   │       └── multi/
│   └── new_geometry/
│       └── gpt-4o-mini-ca/
│           └── multi/
└── user_session_logs/          # 会话日志
    ├── new_algebra/
    └── new_geometry/
```

## 输出文件

### 日志文件
- 位置: `./log/new_{category}/gpt-4o-mini-ca/multi/{mode}.out`
- 内容: 运行日志和错误信息

### 会话记录
- 位置: `./user_session_logs/new_{category}/multi/gpt-4o-mini-ca/traj_gpt-4o-mini-ca_{mode}_multiTools_new_{start_index}.jsonl`
- 内容: 每个问题的详细交互记录

## 故障排除

### 1. API 连接失败
- 检查网络连接
- 验证 API Key 是否正确
- 确认 Base URL 可访问

### 2. 缺少数据文件
确保以下文件存在：
- `algebra_knowledge.json`
- `geometry_knowledge.json`
- `new_algebra_total.json`
- `new_geometry_total.json`

### 3. 依赖问题
```bash
pip install openai torch transformers numpy tqdm requests
```

### 4. 权限问题
```bash
chmod +x test_gpt_config.py
chmod +x run_gpt_agent.py
chmod +x setup_environment.py
```

## 性能优化

### 1. 调整参数
在 `config.py` 中修改：
- `temperature`: 控制输出随机性（0-1）
- `top_p`: 核采样参数（0-1）
- `max_gen_len`: 最大生成长度

### 2. 批量处理
使用 `--start_index` 参数分批处理大量问题。

### 3. 监控资源
- 监控 API 调用频率
- 注意 token 使用量
- 检查内存使用情况

## 注意事项

1. **API 限制**: 注意 API 调用频率限制
2. **成本控制**: GPT-4o-mini 相对便宜，但仍需注意使用量
3. **数据安全**: API Key 已硬编码，生产环境建议使用环境变量
4. **错误处理**: 脚本包含重试机制，但网络问题可能导致失败

## 联系支持

如果遇到问题，请检查：
1. 网络连接
2. API Key 有效性
3. 数据文件完整性
4. 依赖包版本
