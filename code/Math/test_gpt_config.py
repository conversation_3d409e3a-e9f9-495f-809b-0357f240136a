#!/usr/bin/env python3
"""
测试 GPT 配置的简单脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 直接导入配置，避免复杂依赖
from config import OPENAI_CONFIG, MODEL_CONFIG

def test_gpt_connection():
    """测试 GPT API 连接"""
    print("正在测试 GPT API 连接...")

    try:
        # 直接使用 OpenAI 客户端
        from openai import OpenAI

        client = OpenAI(
            api_key=OPENAI_CONFIG["api_key"],
            base_url=OPENAI_CONFIG["base_url"]
        )

        # 测试简单的数学问题
        response = client.chat.completions.create(
            model=OPENAI_CONFIG["model_name"],
            messages=[
                {"role": "system", "content": "你是一个数学助手，请帮助解决数学问题。"},
                {"role": "user", "content": "请计算 2 + 3 = ?"}
            ],
            temperature=MODEL_CONFIG["temperature"],
            top_p=MODEL_CONFIG["top_p"],
            max_tokens=MODEL_CONFIG["max_gen_len"],
            timeout=OPENAI_CONFIG["timeout"]
        )

        print(f"✅ API 连接成功!")
        print(f"模型: {OPENAI_CONFIG['model_name']}")
        print(f"响应: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ API 连接失败: {e}")
        return False

def test_math_planning():
    """测试数学问题规划功能"""
    print("\n正在测试数学问题规划功能...")

    try:
        from openai import OpenAI

        client = OpenAI(
            api_key=OPENAI_CONFIG["api_key"],
            base_url=OPENAI_CONFIG["base_url"]
        )

        test_problem = "解方程: 2x + 5 = 13"

        # 模拟规划提示
        planning_prompt = f"""
        你是一个数学问题解决专家。请为以下数学问题制定解决计划：

        问题: {test_problem}

        请提供详细的解决步骤规划。
        """

        response = client.chat.completions.create(
            model=OPENAI_CONFIG["model_name"],
            messages=[
                {"role": "system", "content": "你是一个数学问题解决专家，擅长制定解题计划。"},
                {"role": "user", "content": planning_prompt}
            ],
            temperature=MODEL_CONFIG["temperature"],
            top_p=MODEL_CONFIG["top_p"],
            max_tokens=MODEL_CONFIG["max_gen_len"],
            timeout=OPENAI_CONFIG["timeout"]
        )

        print(f"✅ 规划功能测试成功!")
        print(f"问题: {test_problem}")
        print(f"规划: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ 规划功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("GPT 配置测试")
    print("=" * 50)
    
    # 显示配置信息
    print(f"API Base URL: {OPENAI_CONFIG['base_url']}")
    print(f"模型名称: {OPENAI_CONFIG['model_name']}")
    print(f"Temperature: {MODEL_CONFIG['temperature']}")
    print(f"Top P: {MODEL_CONFIG['top_p']}")
    print(f"Max Gen Length: {MODEL_CONFIG['max_gen_len']}")
    print("-" * 50)
    
    # 运行测试
    success_count = 0
    total_tests = 2
    
    if test_gpt_connection():
        success_count += 1
    
    if test_math_planning():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过! 您的 GPT 配置正常工作。")
    else:
        print("⚠️  部分测试失败，请检查配置。")
    print("=" * 50)
