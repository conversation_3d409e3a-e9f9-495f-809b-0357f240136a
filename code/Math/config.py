# 配置文件
# OpenAI API 配置
OPENAI_CONFIG = {
    "api_key": "sk-9GlGuB7jKnaoifXTdnMZwVTMKLLGhXdScdJ9n4fmuaGRf3bF",
    "base_url": "https://api.chatanywhere.tech/v1",
    "model_name": "gpt-4o-mini-ca",
    "timeout": 600,
    "max_retries": 5
}

# 模型配置
MODEL_CONFIG = {
    "temperature": 0.0,
    "top_p": 0.9,
    "max_gen_len": 1024
}

# 路径配置
PATHS = {
    "bert_model": "./Bert",
    "algebra_knowledge": "./algebra_knowledge.json",
    "geometry_knowledge": "./geometry_knowledge.json",
    "algebra_data": "./new_algebra_total.json",
    "geometry_data": "./new_geometry_total.json",
    "log_dir": "./log",
    "session_log_dir": "./user_session_logs"
}
