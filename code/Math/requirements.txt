# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: win-64
annotated-types==0.6.0=pypi_0
anyio==4.3.0==pypi_0
ca-certificates==2024.3.11==haa95532_0
cachetools==5.3.3==pypi_0
certifi==2024.2.2==pypi_0
charset-normalizer==3.3.2==pypi_0
colorama==0.4.6==pypi_0
distro==1.9.0==pypi_0
exceptiongroup==1.2.1==pypi_0
filelock==3.14.0==pypi_0
fsspec==2024.3.1==pypi_0
google-ai-generativelanguage==0.6.2==pypi_0
google-api-core==2.18.0==pypi_0
google-api-python-client==2.127.0==pypi_0
google-auth==2.29.0==pypi_0
google-auth-httplib2==0.2.0==pypi_0
google-generativeai==0.5.2==pypi_0
googleapis-common-protos==1.63.0==pypi_0
grpcio==1.62.2==pypi_0
grpcio-status==1.62.2==pypi_0
h11==0.14.0==pypi_0
httpcore==1.0.5==pypi_0
httplib2==0.22.0==pypi_0
httpx==0.27.0==pypi_0
huggingface-hub==0.22.2==pypi_0
idna==3.7==pypi_0
intel-openmp==2021.4.0==pypi_0
jinja2==3.1.3==pypi_0
markupsafe==2.1.5==pypi_0
mkl==2021.4.0==pypi_0
mpmath==1.3.0==pypi_0
networkx==3.2.1==pypi_0
numpy==1.26.4==pypi_0
openai==1.23.6==pypi_0
openssl==3.0.13==h2bbff1b_0
packaging==24.0==pypi_0
pip==23.3.1==py39haa95532_0
proto-plus==1.23.0==pypi_0
protobuf==4.25.3==pypi_0
pyasn1==0.6.0==pypi_0
pyasn1-modules==0.4.0==pypi_0
pydantic==2.7.1==pypi_0
pydantic-core==2.18.2==pypi_0
pyparsing==3.1.2==pypi_0
python==3.9.19==h1aa4202_0
python-dotenv==1.0.1==pypi_0
pyyaml==6.0.1==pypi_0
regex==2024.4.28==pypi_0
requests==2.31.0==pypi_0
rsa==4.9==pypi_0
safetensors==0.4.3==pypi_0
setuptools==68.2.2==py39haa95532_0
sniffio==1.3.1==pypi_0
sqlite==3.41.2==h2bbff1b_0
sympy==1.12==pypi_0
tbb==2021.12.0==pypi_0
tokenizers==0.19.1==pypi_0
torch==2.3.0==pypi_0
tqdm==4.66.2==pypi_0
transformers==4.40.1==pypi_0
typing-extensions==4.11.0==pypi_0
tzdata==2024a==h04d1e81_0
uritemplate==4.1.1==pypi_0
urllib3==2.2.1==pypi_0
vc==14.2==h21ff451_1
vs2015_runtime==14.27.29016==h5e58377_2
wheel==0.41.2==py39haa95532_0